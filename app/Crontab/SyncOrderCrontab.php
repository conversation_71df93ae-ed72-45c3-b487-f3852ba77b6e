<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2022/8/10
 * Time: 22:24
 */

namespace App\Crontab;


use App\Components\Facade4Api\Order\OrderServiceFactory;
use App\Constants\PlatformConst;
use App\Constants\StatusConstant;
use App\Dao\QueueTaskInfoDao;
use App\Dao\Shop\ShopDao;
use App\Repository\Base\QueueTaskInfoRepository;
use App\Repository\Shop\ShopRepository;
use App\Service\Order\PtOrderSyncService;
use App\Utils\Container;
use App\Utils\DateTimeUtil;
use App\Utils\Environment;
use App\Utils\Log;
use App\Utils\SelfLogger;
use Carbon\Carbon;
use Hyperf\Command\Annotation\Command;
use Hyperf\Command\Command as HyperfCommand;
use Hyperf\Contract\StdoutLoggerInterface;
use Hyperf\Crontab\Annotation\Crontab;
use Hyperf\Database\Model\Builder;
use Hyperf\Di\Annotation\Inject;
use Psr\Container\ContainerInterface;

#[Crontab(rule: "0 * * * * *", singleton: true, onOneServer: true,callback: "handle", memo: "平台同步订单")]
#[Command]
class SyncOrderCrontab extends HyperfCommand
{
    #[Inject]
    protected StdoutLoggerInterface $logger;
    #[Inject]
    protected ShopRepository $shopRepository;

    #[Inject]
    protected QueueTaskInfoRepository $queueTaskInfoRepository;

    #[Inject]
    protected  PtOrderSyncService $ptOrderSyncService;

    private string $logPrefix = '';
    private bool $isUpdateSysSyncAt = true;

    public function __construct(protected ContainerInterface $container)
    {
        parent::__construct('sync:orders');
    }

    public function handle()
    {
//        SelfLogger::info("SyncPtOrderTask.handle start");
        $this->logPrefix = 'PtSyncOrderTask:normal';
        // todo 暂时先用抖音测试
        $orderService = OrderServiceFactory::create(PlatformConst::DY);

        $query = $this->shopRepository->getQueryByAuthAvailable();

        $querySupplement = clone $query;
        // 设定上限,防止一直执行
        $query->where(function (Builder $query) {
            $query->where('last_sync_at', '<', Carbon::now()->toDateTimeString());
            $query->orWhereNull('last_sync_at');
        });

        $platformType = $orderService->getPlatformType();
        $batchNo = $this->ptOrderSyncService->getSyncBatchNo();
        $query->chunkById(100, function ($shops) use ($platformType, $batchNo) {
            foreach ($shops as $shop) {
                $redis = Container::getRedis();
                $lockKey = 'cron:cron-sync-order:' . $shop->id;
                // 9分钟内不会重复执行
                $isSet = $redis->set($lockKey, 1, ['nx', 'ex' => 60 * 9]);
                $isSet = true; // 临时去掉锁
                if (!$isSet) {
                    $this->logInfo('重复执行跳过', [
                        'shop_id' => $shop->id,
                        'isset' => $isSet,
                        'access_token' => $shop->access_token,
                    ]);
                    continue;
                }

                $shop = $this->shopRepository->findById($shop->id);

                //首次授权拉取未付款
                $isFirstPull = false;
                $beginAt = (string)$shop->last_sync_at ?: DateTimeUtil::toDateTimeString(strtotime('-7 day'));
                $endAt = DateTimeUtil::toDateTimeString(strtotime('-30 second'));
                $queueTask = $this->queueTaskInfoRepository->create([
                    'batch_no' => 'sync_pt_orders:' . $batchNo,
                    'shop_id' => $shop->id,
                    'task_status' => StatusConstant::WAITING,
                    'task_param' => json_encode([
//                'shop_id' => $shop->id,
                        'begin_at' => $beginAt,
                        'end_at' => $endAt,
                    ]),
                ]);
                if (strtotime($beginAt) < strtotime($endAt)) {
                    //系统同步更新系统同步时间
                    $this->ptOrderSyncService->handleSyncOrders($shop, $beginAt, $endAt, $queueTask->id, $isFirstPull,
                        $lockKey, $this->isUpdateSysSyncAt);
//                    $this->handleSyncRemarks($shop, $beginAt, $endAt);
                }
            }
        });

        $this->isUpdateSysSyncAt = false;
        $this->logPrefix = 'PtSyncOrderTask:supplement';
//        if (in_array(config('app.platform'), [PlatformConst::DY, PlatformConst::KS])) {
//        if (config('app.platform') != PlatformConst::TAOBAO) { // 淘宝不补单
        if (false) {
            // 补充同步（补单）
            // todo 优化 count订单 和 平台接口对比数量，不一致才同步
            $querySupplement->chunkById(1000, function ($shops) use ($platformType) {
                foreach ($shops as $shop) {
                    $beginAt = date_YmdHis(strtotime('-30 minute'));
                    $endAt = date_YmdHis(strtotime('-20 minute'));
                    if (strtotime($beginAt) < strtotime($endAt)) {
                        $this->ptOrderSyncService->handleSyncOrders($shop, $beginAt, $endAt);
                    }
                }
            });
        }
    }
//        } finally {
//            Context::destroy(REQUEST_ID);
//        }
//    }
//    /**
//     * 同步备注
//     * @param $shop
//     * @param $beginAt
//     * @param $endAt
//     * @return void
//     */
//    public function handleSyncRemarks($shop,$beginAt,$endAt){
//        try {
//            $orderRemarkService=new OrderRemarkService();
//            $orderRemarkService->handleSyncRemarks($shop, $beginAt, $endAt);
//        }catch (\Throwable $ex){
//            \Log::error("同步备注异常",["exception"=>$ex,"shop"=>$shop,"beginAt"=>$beginAt,"endAt"=>$endAt]);
//        }
//    }



    private function logInfo(string $string, array $data = [])
    {
        Log::info($this->logPrefix . ':' . $string, $data);
    }

    private function logError(string $string, array $data = [])
    {
        Log::error($this->logPrefix . ':' . $string, $data);
    }

    private function logErrorException(string $string, $e, array $data = [])
    {
        Log::errorException($this->logPrefix . ':' . $string, $e, $data);
    }

}