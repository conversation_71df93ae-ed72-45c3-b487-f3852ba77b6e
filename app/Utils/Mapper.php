<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2022/9/8
 * Time: 12:47
 */

namespace App\Utils;

use Hyperf\DbConnection\Model\Model;
use JsonMapper;

class Mapper
{
    private static JsonMapper $jsonMapperByArr;
    private static JsonMapper $jsonMapperByObj;

    public static function mapToObject(array $data, mixed $obj): mixed
    {
        $jsonMapper = self::getJsonMapperByArr();
        $jsonMapper->map($data, $obj);
        return $obj;
    }


    public static function objectToObject(object $fromObj, mixed $toObj)
    {
        $jsonMapper = self::getJsonMapperByObject();
        $jsonMapper->map($fromObj, $toObj);
        return $toObj;
    }

    public static function modelToModel(Model $fromModel, mixed $toModel)
    {
        $toModel->setRawAttributes($fromModel->getAttributes());
        $toModel->setRelations($fromModel->getRelations());
        return $toModel;
    }

    public static function modelToObject(Model $fromModel, mixed $toObj, bool $isCamelCase = true)
    {
        $attributes = $fromModel->getAttributes();

        if($isCamelCase){
            self::mapToObjectByCamel($attributes,$toObj);
        }else {
            self::mapToObject($attributes, $toObj);
        }
        return $toObj;
    }

    public static function mapToModel(array $data, Model $toModel): Model
    {
        foreach ($data as $key => $datum) {
            $toModel->setAttribute($key, $datum);
        }
        return $toModel;
    }

    public static function objectToArray(object|array|null $obj): array
    {
        if (empty($obj)) {
            return [];
        }
        return json_decode(json_encode($obj), true); // TODO 这种方法不兼容，换个方案
/*        $_arr = is_object($obj) ? get_object_vars($obj) : $obj;
        $arr = null;
        foreach ($_arr as $key => $val) {
            if(!isset($val)){
                continue;
            }
            $val = (is_array($val)) || is_object($val) ? self::objectToArray($val) : $val;
            $arr[$key] = $val;
        }
        return $arr;*/
    }

    /**
     * 复制数组字段值到对象 转换为下划线（蛇形）
     * @param array $data
     * @param mixed $obj
     * @return mixed
     */
    public static function mapToObjectBySnake(array $data, mixed $obj): mixed
    {
        $data = CommonUtil::recursionSnakeCase($data);
        Log::info('recursionSnakeCase', [$data]);
        return static::mapToObject($data, $obj);
    }

    /**
     * 复制数组字段值到对象 转换为小驼峰
     * @param array $data
     * @param mixed $obj
     * @return mixed
     */
    public static function mapToObjectByCamel(array $data, mixed $obj): mixed
    {
        $data = CommonUtil::recursionCamelCase($data);
        return static::mapToObject($data, $obj);
    }

    /**
     * 复制数组字段值到模型对象 转换为下划线（蛇形）
     * @param array $data
     * @param mixed $obj
     * @return Model
     */
    public static function mapToModelBySnake(array $data, mixed $obj): Model
    {
        $data = CommonUtil::recursionSnakeCase($data);
        return static::mapToModel($data, $obj);
    }

    protected static function getJsonMapperByObject(): JsonMapper
    {
        if (empty(static::$jsonMapperByObj)) {
            $jsonMapper = new JsonMapper();
            $jsonMapper->bEnforceMapType = true;
            static::$jsonMapperByObj = $jsonMapper;
        }
        return static::$jsonMapperByObj;
    }

    protected static function getJsonMapperByArr(): JsonMapper
    {
        if (empty(static::$jsonMapperByArr)) {
            $jsonMapper = new JsonMapper();
            $jsonMapper->bEnforceMapType = false;
            static::$jsonMapperByArr = $jsonMapper;
        }
        return static::$jsonMapperByArr;
    }
}