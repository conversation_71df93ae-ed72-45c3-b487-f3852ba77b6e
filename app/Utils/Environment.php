<?php

namespace App\Utils;

use App\Constants\PlatformConst;

/**
 * 环境
 */
class Environment
{

    public static function isProd(): bool
    {

        return \Hyperf\Config\config('app_env') == 'prod';
    }

    public static function isPdd($platform): bool
    {
        return $platform == PlatformConst::PDD;
    }
    public static function isTb($platform): bool
    {
        return $platform == PlatformConst::TAOBAO;
    }
    public static function isJd($platform)
    {
        return $platform == PlatformConst::JD;
    }
    public static function isKs($platform)
    {
        return $platform == PlatformConst::KS;
    }
    public static function isDy($platform)
    {
        return $platform == PlatformConst::DY;
    }
    public static function isWx($platform)
    {
        return $platform == PlatformConst::WX;
    }
    public static function isWxsp($platform)
    {
        return $platform == PlatformConst::WXSP;
    }
    public static function isXhs($platform)
    {
        return $platform == PlatformConst::XHS;
    }
    public static function isAlbb($platform)
    {
        return $platform == PlatformConst::ALBB;
    }
    public static function isCn($platform)
    {
        return $platform == PlatformConst::CN;
    }
    public static function isAlc2m($platform)
    {
        return $platform == PlatformConst::ALC2M;
    }

}
