<?php

namespace App\Components\Facade4Api\Logistic\Impl;

use App\Components\Facade4Api\Client\PddClient;
use App\Components\Facade4Api\Logistic\AbstractLogisticFacade;
use App\Components\Facade4Api\Logistic\FaLogisticDetailItem;
use App\Components\Facade4Api\Logistic\FaLogisticDetailResultItem;
use App\Components\Facade4Api\Logistic\FaLogisticDetailsRequest;
use App\Components\Facade4Api\Logistic\FaLogisticDetailsResponse;
use App\Constants\LogisticsConstant;
use App\Utils\Log;
use Hyperf\Utils\Parallel;

class PddLogisticImpl extends AbstractLogisticFacade
{
    private array $pddAction2Status = [
        'GOT' => 1,
        'SEND' => 5,
        'SIGN' => 3,
        'REJECTION' => 4,
        'RETURN' => 4,
        'ARRIVAL' => LogisticsConstant::STATUS_NODE_PAUSE,
        'DEPARTURE' => LogisticsConstant::STATUS_DISPATCH_PAUSE,
    ];

    /**
     * 获取面单的物流详情
     * @param string $accessToken
     * @param  FaLogisticDetailResultItem[] $items
     * @return FaLogisticDetailResultItem[]
     */
    function getLogisticDetails(string $accessToken, array $items): array
    {
        $parallel = new Parallel(5);
        foreach ($items as $item) {
            $parallel->add(function () use ($accessToken,$item) {
                return $this->getLogisticDetail($accessToken,$item);
            });
        }
        return $parallel->wait();


    }

    public function getLogisticDetail(string $accessToken, FaLogisticDetailItem $item): FaLogisticDetailResultItem
    {
        /**
         * @var  FaLogisticDetailResultItem $result
         */
        $result =null;
        $waybillCode = $item->waybillCode;
        try {
            $pddClient = PddClient::newInstance($accessToken);
            $pddClient->appkey="b118cee16cd544ed9fabedccc089c836";
            $pddClient->secretKey="59bd182e7cd4bba1e45d9313df499bf640fa0637";
            $response = $pddClient->execute('pdd.logistics.ordertrace.get', [
                'mail_no' => $waybillCode,
                'company_code' => $item->wpCode,
            ], 'post');

            $result =$this->formatLogisticDetailResult($waybillCode,$response);
        } catch (\Exception $e) {
            $result=FaLogisticDetailResultItem::fail($waybillCode, $e->getMessage());

        }
        return $result;
    }

    /**
     * 格式化物流详情
     * @param string $waybillCode
     * @param array $resp
     * @return FaLogisticDetailResultItem
     */
    private function formatLogisticDetailResult(string $waybillCode,array $resp): FaLogisticDetailResultItem
    {
        $faLogisticDetailResultItem =new FaLogisticDetailResultItem();
        $faLogisticDetailResultItem->waybillCode = $waybillCode;
        $faLogisticDetailResultItem->success = true;
        Log::info('pdd logistics response', [$resp]);
        $list = $resp['logistics_ordertrace_get_resposne']['trace_list'];

        if (empty($list)) {
            $faLogisticDetailResultItem->status=-1;
            $faLogisticDetailResultItem->logisticsStatus = LogisticsConstant::LOGISTICS_STATUS_LS_SENDED;
            return $faLogisticDetailResultItem;
        }
        $logisticsStatus = LogisticsConstant::LOGISTICS_STATUS_LS_OTHER;
        $last = $list[0];
        // GOT 揽件 SEND 派件 SIGN 签收 ARRIVAL 到件 DEPARTURE 发件 FAIL 问题件 REJECTION 拒签 STAY_IN_WAREHOUSE 留仓 SIGN_ON_BEHALF 代收点代签 OTHER 其他 RETURN 退件 IN_CABINET 入柜/入代收点 OUT_CABINET 出柜/出代收点
        $action = $last['action'];
        if ($action === 'GOT') {
            $logisticsStatus = LogisticsConstant::LOGISTICS_STATUS_LS_ONLY_RECEIVED;
        } else if ($action === 'SEND' || $action === 'ARRIVAL' || $action === 'DEPARTURE' || $action === 'STAY_IN_WAREHOUSE' || $action === 'SIGN_ON_BEHALF' || $action === 'IN_CABINET' || $action === 'OUT_CABINET') {
            $logisticsStatus = LogisticsConstant::LOGISTICS_STATUS_LS_TRANSPORTING;
        } else if ($action === 'SIGN') {
            $logisticsStatus = LogisticsConstant::LOGISTICS_STATUS_LS_SIGNED;
        } else if ($action === 'FAIL' || $action === 'REJECTION' || $action === 'RETURN') {
            $logisticsStatus = LogisticsConstant::LOGISTICS_STATUS_LS_ERROR;
        }
        // TMS_NOT_ACCEPT 已取号未揽收; TMS_ACCEPT 已揽收; TMS_DELIVERING 派件中;TMS_SIGN 妥投; TMS_FAILED 拒收; TMS_ERROR 配送异常; TMS_TRANSPORT 运输中;

        //如果是结束动作，就同步状态就是1（同步完成），否则就是0（同步中）
        $faLogisticDetailResultItem->syncStatus = in_array($action, LogisticsConstant::PDD_END_ACTIONS) ? LogisticsConstant::SYNC_FINISHED : LogisticsConstant::SYNC_ING;
        $faLogisticDetailResultItem->status = $this->pddAction2Status[$action] ?? -1;
        //只保存了最后的trace，没必要保存完整的traceList，如果需要用的话，就实时取平台取
        $faLogisticDetailResultItem->latestTrace = $last['desc'];
        $faLogisticDetailResultItem->latestUpdatedAt = $last['status_time']??null;
//        $faLogisticDetailResultItem->traceList = json_encode($list, JSON_UNESCAPED_UNICODE);
        $faLogisticDetailResultItem->logisticsStatus = $logisticsStatus;
        return $faLogisticDetailResultItem;

    }


}