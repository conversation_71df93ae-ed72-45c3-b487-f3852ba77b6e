<?php
namespace App\Components\Facade4Api;

class Page
{
    public int $currentPage;
    public int $pageSize;
    public int $total;
    public array $data;
    public bool $hasNext;
    public string $pcursor;

    public static function newInstance($currentPage,$pageSize,$data,$hasNext,$total):Page{
        $page = new Page();
        $page->currentPage=$currentPage;
        $page->pageSize=$pageSize;
        $page->data=$data;
        $page->hasNext=$hasNext;
        $page->total=$total;
        return $page;
    }
}