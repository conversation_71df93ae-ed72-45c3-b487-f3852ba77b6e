<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2022/9/16
 * Time: 13:50
 */

namespace App\Components\Facade4Api\Order\Response;

use App\Request\PtOrder\Pt\PtOrderDeliverPack;

class PackDeliverResponse
{
    /**
     * @var bool 是否成功
     */
    public bool $success;
    public string $tid;
    public string $waybillCode;
    public string $errMsg;
    /**
     * 包裹ID,本身平台发货用不了这个，主要是为了成功以后，用于处理包裹的状态
     * @var int|null
     */
    public ?int $packageId;
    /**
     * 一单多包的发货，发货接口返回，便于后续处理
     * @var array<PtOrderDeliverPack>
     */
    public ?array $packs;
}