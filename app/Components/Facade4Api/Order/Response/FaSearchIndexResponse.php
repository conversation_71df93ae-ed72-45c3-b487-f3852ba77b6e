<?php

namespace App\Components\Facade4Api\Order\Response;

use App\Components\Facade4Api\Order\Request\FaSearchIndexParam;

class FaSearchIndexResponse
{
    /**
     * 用于匹配请求和响应
     * @var string|null 请求ID`
     */
    public ?string $requestId = null;

    /**
     * @var string 字符串
     *
     */
    public string $text;

    /**
     * @var string 加密类型
     */
    public string $type;

    /**
     * @var bool    是否成功
     */
    public bool $success;
    /**
     * 索引字段
     * @var string|null
     */
    public ?string $searchIndex = null;

    /**
     * 是不是手机号
     * @return bool
     */
    public  function isPhone():bool{
        return $this->type == FaSearchIndexParam::TYPE_PHONE;
    }

    /**
     * 是不是名字
     * @return bool
     */
    public function isName():bool{
        return $this->type == FaSearchIndexParam::TYPE_NAME;
    }
}