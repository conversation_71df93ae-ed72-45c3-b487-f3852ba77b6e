<?php

namespace App\Components\Facade4Api\Order\Response;

use App\Dto\PtOrderDto;
use App\Utils\Mapper;

class OrderListResponse
{
    public bool $hasNext = false;
    public int $total = -1;
    /**
     * 翻页游标
     * @var string
     */
    public string $pageCursor = '';
    public int $requestPage;
    /**
     * @var array
     */
    protected array $orderList = [];

    /**
     * @return array
     */
    public function getOrderList(): array
    {
        return $this->orderList;
    }

    /**
     * @param array|null $orderList
     */
    public function setOrderList(?array $orderList): void
    {
        if (empty($orderList)){
            $orderList = [];
        }
        $this->orderList = $orderList;
    }
}