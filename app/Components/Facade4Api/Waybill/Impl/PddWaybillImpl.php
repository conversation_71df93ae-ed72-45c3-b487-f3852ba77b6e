<?php

namespace App\Components\Facade4Api\Waybill\Impl;

use App\Components\Facade4Api\Client\PddClient;
use App\Components\Facade4Api\Exception\ErrorConstants;
use App\Components\Facade4Api\Waybill\AbstractWaybillFacade;
use App\Components\Facade4Api\Waybill\FaApplyWaybillCodeRequest;
use App\Components\Facade4Api\Waybill\FaApplyWaybillCodeResponse;
use App\Components\Facade4Api\Waybill\FaPackage;
use App\Components\Facade4Api\Waybill\FaPackageWaybillResult;
use App\Components\Facade4Api\Waybill\FaRecycleWaybillCodeResponse;
use App\Components\Facade4Api\Waybill\FaSender;
use App\Components\Facade4Api\Waybill\FaServiceAttribute;
use App\Components\Facade4Api\Waybill\FaServiceInfoMeta;
use App\Components\Facade4Api\Waybill\PtBranchAccount;
use App\Components\Facade4Api\Waybill\PtShippAddress;
use App\Components\Facade4Api\Waybill\PtWaybillAccount;
use App\Constants\WaybillConstant;
use App\Model\Template;
use App\Utils\CommonUtil;
use App\Utils\IdGenerator;
use App\Utils\Log;
use App\Utils\StrUtil;
use Hyperf\Collection\Arr;
use Hyperf\Coroutine\Parallel;
use Hyperf\Utils\Collection;


class PddWaybillImpl extends AbstractWaybillFacade
{

    const ZIMUJIANMAP = [
        'BESTQJT',
        'DEBANGWULIU',
        'SFKY',
        'HOAU',
        'ANKY',
        'YDKY',
        'SXJD',
        'ZTOKY',
        'YDKY',
    ];

    public function getTemplateFile(Template $template): string
    {
        $str = "<?xml version='1.0'?>";
        $width = $template->commonTemplate->width * 8 - 50;
        $height = $template->commonTemplate->height * 8;
        $str .= '<page width="' . $width . '" height="' . $height . '">';
        $str .= '<layout width="' . $width . '" height="' . $height . '" left="0" top="0">';
        foreach (json_decode($template->custom_config, true) as $item) {
            $left = $item['left'] * 2;
            $top = $item['top'] * 2;
            $width = $item['width'] * 2;
            $height = $item['height'] * 2;
            $fontFamily = $item['fontFamily'];
            $fontWeight = $item['fontWeight'];
            $fontSize = intval($item['fontSize'])* 2;
            if ($item['value'] !== '<%=data.watermark%>') {
                $str .= ' <text left="' . $left . '" top="' . $top . '" width="' . $width . '" height="' . $height . '" value="' . $item['value'] . '" fontSize="' . $fontSize . '" fontWeight="' . $fontWeight . '" fontFamily="' . $fontFamily . '"/>';
            } else {
                $str .= '<waterrmark left="' . $left . '" top="' . $top . '" width="' . $width . '" height="' . $height . '" value="' . $item['value'] . '" fontSize="' . $fontSize . '" align="center" type="1" fontWeight="bold" fontFamily="Arial" Alpha="150"/>';
            }
        }
        $str .= "</layout></page>";
        return $str;
    }

    public function getWaybillAccount(string $accessToken, string $serviceId, string $wpCode): array
    {
        $client = PddClient::newInstance($accessToken);
        $result = [];
        $data = array();
        if ($wpCode) {
            $data['wp_code'] = $wpCode;
        }
        $res = $client->execute("pdd.waybill.search", $data, 'post');
        $resp = Arr::get($res, 'pdd_waybill_search_response');
        if (!$resp) {
            return [];
        }
        $list = Arr::get($resp, 'waybill_apply_subscription_cols');
        if (empty($list)) {
            return [];
        }
        foreach ($list as $waybill_apply_subscription_col) {
            $result[] = $this->formatToWaybillAccount($waybill_apply_subscription_col);
        }
        return CommonUtil::recursionCamelCase(array_values($result));
    }

    public function formatToWaybillAccount($value): PtWaybillAccount
    {
        $waybillAccount = new PtWaybillAccount();
        $waybillAccount->wpCode = $value['wp_code'];
        $waybillAccount->wpType = $value['wp_type'] > 1 ? 1 : 2;
        $branchAccounts = [];
        foreach ($value['branch_account_cols'] as $key => $account_col) {
            /**
             * @var FaServiceInfoMeta[] $serviceInfoCols
             */
            $serviceInfoCols = [];
            foreach ($account_col['service_info_cols'] as $serviceInfo) {
                /**
                 * @var FaServiceAttribute[] $serviceAttributes
                 */
                $serviceAttributes = [];
                foreach ($serviceInfo['service_attributes'] as $serviceDesc) {

//                    $serviceAttributes[] = [
//                        'attribute_name' => $serviceDesc['attribute_name'],
//                        'attribute_type' => $serviceDesc['attribute_type'],
//                        'attribute_code' => $serviceDesc['attribute_code'],
//                        'type_desc' => $serviceDesc['type_desc']
//                    ];
                    $serviceAttributes[] = new FaServiceAttribute($serviceDesc['attribute_name'],$serviceDesc['attribute_type'],$serviceDesc['attribute_code'],$serviceDesc['type_desc']);
                }
                $serviceInfoCols[] = FaServiceInfoMeta::of($serviceInfo['required'],$serviceInfo['service_desc'],$serviceInfo['service_name'],$serviceInfo['service_code'],$serviceAttributes);


//                $serviceInfoCols[] = [
//                    'required' => $serviceInfo['required'],
//                    'service_desc' => $serviceInfo['service_desc'],
//                    'service_name' => $serviceInfo['service_name'],
//                    'service_code' => $serviceInfo['service_code'],
//                    'service_attributes' => $serviceAttributes
//                ];
            }
            $branchAccount = new PtBranchAccount();
            $branchAccount->branchCode = $account_col['branch_code'] ?? '';
            $branchAccount->branchName = $account_col['branch_name'] ?? '';
            $branchAccount->quantity = $account_col['quantity'] ?? 0;
            $branchAccount->cancelQuantity = $account_col['cancel_quantity'] ?? 0;
            $branchAccount->recycledQuantity = $account_col['recycled_quantity'] ?? 0;
            $branchAccount->allocatedQuantity = $account_col['allocated_quantity'] ?? 0;
            $addresses = [];
            foreach ($account_col['shipp_address_cols'] as $add) {
                $addre = new PtShippAddress();
                $addre->province = Arr::get($add, "province", '');
                $addre->city = Arr::get($add, "city", '');
                $addre->district = Arr::get($add, "district", '');
                $addre->detail = Arr::get($add, "detail", '');
                $addresses[] = $addre;
            }
            $branchAccount->shippAddressArr = $addresses;
            $branchAccount->serviceInfoCols = $serviceInfoCols;

            $branchAccounts[$key] = $branchAccount;
        }
        $waybillAccount->branchAccountCols = $branchAccounts;
        return $waybillAccount;

    }

    /**
     * @param string $accessToken
     * @param FaApplyWaybillCodeRequest $faApplyWaybillCodeRequest
     * @return FaApplyWaybillCodeResponse
     */
    public function createWaybillData(string $accessToken, FaApplyWaybillCodeRequest $faApplyWaybillCodeRequest): FaApplyWaybillCodeResponse
    {
        Log::info("获取拼多多电子面单", ["accessToken" => $accessToken, "wpCode" => $faApplyWaybillCodeRequest->wpCode,
            "sender" => $faApplyWaybillCodeRequest->sender]);
        $faApplyWaybillCodeResponse = new FaApplyWaybillCodeResponse();
        $client = PddClient::newInstance($accessToken);
        $orderCreateApiParams = $this->buildOrderInfos($faApplyWaybillCodeRequest);
        $packagesGroupByRequestId = $faApplyWaybillCodeRequest->getPackagesKeyByRequestId();

//        foreach ($orderCreateApiParams as $param) {
//            Log::info('self params:', [$param]);
//            $response = $client->execute("pdd.waybill.get", $param, 'post');
//            $this->extractResults($response, $packagesGroupByRequestId, $faApplyWaybillCodeResponse);
//        }
        $parallel = new Parallel(5);
        foreach ($orderCreateApiParams as $param) {
            Log::info('self params:',[$param]);
            $parallel->add(function () use ($param, $client,$packagesGroupByRequestId, $faApplyWaybillCodeResponse) {
                try {
                    $response = $client->execute("pdd.waybill.get", $param, 'post');
//                return $client->handleResponse($response);
                    $this->extractResults($response, $packagesGroupByRequestId, $faApplyWaybillCodeResponse);
                }catch (\Throwable $throwable){
                    Log::errorException("获取面单失败", $throwable);
                    $faPackageWaybillResult = new FaPackageWaybillResult();
                    $faPackageWaybillResult->success = false;
                    $faPackageWaybillResult->errCode = ErrorConstants::GET_WAYBILL_FAIL[0];
                    $faPackageWaybillResult->errMsg = $throwable->getMessage();
                    $faApplyWaybillCodeResponse->appendFail($faPackageWaybillResult);
                }
            });
        }
        $responseList = $parallel->wait();
        $packagesGroupByRequestId = $faApplyWaybillCodeRequest->getPackagesKeyByRequestId();
        foreach ($responseList as $resp) {
            Log::info("result:",[$resp]);

        }

        return $faApplyWaybillCodeResponse;
    }

    private function extractResults($waybillCodeResult, array $packagesGroupByRequestId, FaApplyWaybillCodeResponse $faApplyWaybillCodeResponse): void
    {
        Log::info("拼多多取号请求返回", [$waybillCodeResult]);
        $resp = Arr::get($waybillCodeResult, "pdd_waybill_get_response", []);
        $response = Arr::get($resp, "modules", []);
        if (empty($response)) {
            return;
        }
        $successes = [];
        if (Arr::isAssoc($response)) {
            $response = [$response];
        }
        foreach ($response as $responseData) {
            $requestId = $responseData['object_id'];

            $package = $packagesGroupByRequestId[$requestId];
            $faPackageWaybillResult = new FaPackageWaybillResult();
            $faPackageWaybillResult->waybillCode = Arr::get($responseData, "waybill_code");
            $faPackageWaybillResult->parentWaybillCode = Arr::get($responseData, "parent_waybill_code", null);
            $printData = json_decode(Arr::get($responseData, "print_data"), true);
            $encryptedData = $printData['encryptedData'];
            $faPackageWaybillResult->printData = $encryptedData;
            $faPackageWaybillResult->success = true;
            $signature = $printData['signature'];
            $faPackageWaybillResult->sign = $signature;
            $faPackageWaybillResult->requestId = $requestId;
            $faPackageWaybillResult->orderId = $package->orderId . '';
            $faPackageWaybillResult->version = $printData['ver'];
            Log::info("请求返回", [$requestId, $encryptedData, $signature]);
            $successes[] = $faPackageWaybillResult;
        }
        $faApplyWaybillCodeResponse->appendSuccesses($successes);
    }

    private function generateSender(FaSender $sender): array
    {
        //发件人信息
        $senderInfo['mobile'] = $sender->phone;
        $senderInfo['phone'] = '';
        $senderInfo['name'] = $sender->name;
        $senderInfo['address']['province'] = $sender->province;
        $senderInfo['address']['city'] = $sender->city;
        $senderInfo['address']['district'] = $sender->district;
        $senderInfo['address']['town'] = '';
        $senderInfo['address']['detail'] = $sender->detail;
        return $senderInfo;
    }

    private function generateRecipient(FaPackage $package): array
    {
        return [
            'address' => [
                'city' => $package->receiver->city,
                'detail' => $package->receiver->address,
                'district' => $package->receiver->district,
                'town' => $package->receiver->town,
                'province' => $package->receiver->province,
            ],
            'mobile' => $package->receiver->phone,
            'name' => $package->receiver->name,
            'phone' => '',
        ];
    }

    /**
     * 构建取号参数
     *
     * @param FaApplyWaybillCodeRequest $request
     * @return array
     */
    private function buildOrderInfos(FaApplyWaybillCodeRequest $request): array
    {
        $packages = $request->packages;
        //组装面单请求信息
        $tradeOrderInfoDtos = [];
        foreach ($packages as $package) {
            //增值服务
//            $tradeOrderInfoDto['logistics_services'] = "{\"INSURE\": {\"value\": 100}}";
//            if (!empty($template['service_list'])) {
//                $tradeOrderInfoDto['logistics_services'] = $template['service_list'];
//            }
            //如果是子母单，需要创建多条请求
            $count = $package->num;
            $orderIdList = $request->isPtOrder ? [$package->tid . ''] : [$package->orderId . ''];
            if ($count > 1) {//子母件
                $orderIdList = $request->isPtOrder ? [$package->tid . '' . IdGenerator::generateStr()] : [$package->orderId . '' . IdGenerator::generateStr()];
            }
            for ($i = 0; $i < $count; $i++) {
                $tradeOrderInfoDto['object_id'] = $package->requestId;
                //填充增值服务
                $pddFaServiceInfo = $request->getPddFaServiceInfo();
                if(!empty($pddFaServiceInfo)){
                    $tradeOrderInfoDto['logistics_services'] = $pddFaServiceInfo;
                }

                //收件人信息
                $tradeOrderInfoDto['recipient'] = $this->generateRecipient($package);
                $tradeOrderInfoDto['template_url'] = $request->templateUrl;
                $tradeOrderInfoDto['user_id'] = $request->tenantId;
                //订单信息
                $tradeOrderInfoDto['order_info'] = [
                    'order_channels_type' => 'PDD',
                    'trade_order_list' => $orderIdList
                ];
                //包裹信息
                $items = [];
                if (isset($package->packageGoods)) {
                    foreach ($package->packageGoods as $packageGood) {
                        $temp = [];
                        $temp['count'] = $packageGood->shippedNum;
                        $temp['name'] = $packageGood->goodsTitle;
                        $items[] = $temp;
                    }
                }

                $tradeOrderInfoDto['package_info'] = [
                    'id' => IdGenerator::generateStr(),
                    'goods_description' => "",
                    'items' => $items,
                    'volume' => "",
                    'weight' => "",

                    'total_packages_count' => $package->num,
                ];
                $tradeOrderInfoDtos[] = $tradeOrderInfoDto;
            }
        }
        $total = count($tradeOrderInfoDtos);
        Log::info('params:', [$tradeOrderInfoDtos]);
        //快运不允许批量取号
        if ($total < 2) {
            $data = $this->buildCommonRequest($request);
            $data['trade_order_info_dtos'] = $tradeOrderInfoDtos;
            $temp = [];
            $temp['param_waybill_cloud_print_apply_new_request'] = json_encode($data);
            return [$temp];
        }
        $result = [];
        for ($i = 0; $i < $total; $i++) {
            $list = array_slice($tradeOrderInfoDtos, $i, 1);
            //一次最多10个单号
            $data = $this->buildCommonRequest($request);
            $data['trade_order_info_dtos'] = $list;
            $temp = [];
            $temp['param_waybill_cloud_print_apply_new_request'] = json_encode($data);
            $result[] = $temp;
        }
        return $result;
    }

    private function buildCommonRequest(FaApplyWaybillCodeRequest $request): array
    {
        $data = [];
        $data['wp_code'] = $request->wpCode;
        $data['need_encrypt'] = true;
        //组装发件人信息
        $data['sender'] = $this->generateSender($request->sender);
        return $data;
    }

    public function updateWaybillData(string $accessToken, FaApplyWaybillCodeRequest $faApplyWaybillCodeRequest): FaApplyWaybillCodeResponse
    {
        $faApplyWaybillCodeResponse = new FaApplyWaybillCodeResponse();
        $requestList = $this->buildUpdateWaybillParams($faApplyWaybillCodeRequest);
        $parallel = new Parallel(5);
        $packageKeyByRequestId = $faApplyWaybillCodeRequest->getPackagesKeyByRequestId();
        Log::info("更新面单请求", [$requestList]);
        foreach ($requestList as $requestId => $updateRequest) {
            $parallel->add(function () use (
                $accessToken, $updateRequest, $requestId,
                $packageKeyByRequestId, $faApplyWaybillCodeResponse
            ) {
                $package = $packageKeyByRequestId[$requestId];
                $faPackageWaybillResult = new FaPackageWaybillResult();
                $faPackageWaybillResult->requestId = $requestId;
                $faPackageWaybillResult->orderId = $package->orderId . '';
                try {
                    $client = PddClient::newInstance($accessToken);
                    $response = $client->execute('pdd.waybill.update', $updateRequest, 'post');

                    $res = $response['pdd_waybill_update_response'];
                    $waybillCode = $res['waybill_code'];

                    Log::info("更新面单请求返回", [$requestId, $waybillCode, $response]);
                    $printData = json_decode(Arr::get($res, "print_data"), true);
                    $encryptedData = $printData['encryptedData'];
                    $signature = $printData['signature'];
                    $faPackageWaybillResult->printData = $encryptedData;
                    $faPackageWaybillResult->sign = $signature;
                    $faPackageWaybillResult->waybillCode = $waybillCode;
                    $faPackageWaybillResult->success = true;

                    $faApplyWaybillCodeResponse->appendSuccess($faPackageWaybillResult);
                } catch (\Throwable $throwable) {
                    Log::errorException("更新面单失败", $throwable);
                    $faPackageWaybillResult->success = false;
                    $faPackageWaybillResult->errCode = ErrorConstants::GET_WAYBILL_FAIL[0];
                    $faPackageWaybillResult->errMsg = $throwable->getMessage();
                    $faApplyWaybillCodeResponse->appendFail($faPackageWaybillResult);
                }
            });
        }
        $parallel->wait();
        return $faApplyWaybillCodeResponse;
    }

    private function buildUpdateWaybillParams(FaApplyWaybillCodeRequest $faApplyWaybillCodeRequest): array
    {
        $packages = $faApplyWaybillCodeRequest->packages;
        $requestList = [];
        foreach ($packages as $package) {
            $waybillCloudPrintUpdateRequest = [];
            $waybillCloudPrintUpdateRequest['recipient'] = $this->generateRecipient($package);
            $waybillCloudPrintUpdateRequest['sender'] = $this->generateSender($faApplyWaybillCodeRequest->sender);
            $waybillCloudPrintUpdateRequest['waybill_code'] = $package->waybillCode;
            $waybillCloudPrintUpdateRequest['wp_code'] = $faApplyWaybillCodeRequest->wpCode;
            $requestId = $package->requestId;
            $waybillCloudPrintUpdateRequest['object_id'] = $requestId;
            $waybillCloudPrintUpdateRequest['template_url'] = $faApplyWaybillCodeRequest->templateUrl;
            $temp = [];
            $temp['param_waybill_cloud_print_update_request'] = json_encode($waybillCloudPrintUpdateRequest);
            $requestList[$requestId] = $temp;
        }
        return $requestList;
    }

    public function recycle(string $accessToken, string $wpCode, array $waybillCodes): array
    {
        $parallel = new Parallel(5);
        foreach ($waybillCodes as $waybillCode) {
            $parallel->add(function () use ($accessToken, $wpCode, $waybillCode) {
                $faRecycleWaybillCodeResponse = new FaRecycleWaybillCodeResponse();
                $faRecycleWaybillCodeResponse->waybillCode = $waybillCode;

                try {
                    $client = PddClient::newInstance($accessToken);
                    $data = array(
                        'wp_code' => $wpCode,
                        'waybill_code' => $waybillCode
                    );
                    $result = $client->execute('pdd.waybill.cancel', $data, 'post');
                    $faRecycleWaybillCodeResponse->success = $result['pdd_waybill_cancel_response']['cancel_result'];
                    Log::info("回收电子面单", [$waybillCode, $result]);
                } catch (\Exception $ex) {
                    $errMsg = $ex->getMessage();
                    //包含了特定的错误信息，说明是回收成功的
                    if(StrUtil::containsAny($errMsg,WaybillConstant::WAYBILL_REAL_RECYCLE_MSG)) {
                        $faRecycleWaybillCodeResponse->success = true;
                    }else{
                        $faRecycleWaybillCodeResponse->success = false;
                    }
                    $faRecycleWaybillCodeResponse->errMsg = $errMsg;
                }
                return $faRecycleWaybillCodeResponse;
            });
        }
        return $parallel->wait();
    }

    public function isSupportUpdateWaybillData(string $unionWpCode): bool
    {
        return !in_array($unionWpCode, self::ZIMUJIANMAP);
    }
}