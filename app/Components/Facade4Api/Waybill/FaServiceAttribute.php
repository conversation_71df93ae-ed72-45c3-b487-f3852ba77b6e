<?php

namespace App\Components\Facade4Api\Waybill;

/**
 * 增值服务属性
 */
class FaServiceAttribute
{

    public string $attributeName;
    public string $attributeType;
    public string $attributeCode;
    public ?string $typeDesc;
    public ?string $value;

    /**
     * @param string $attributeName
     * @param string $attributeType
     * @param string $attributeCode
     * @param string|null $typeDesc
     * @param string|null $value
     */
    public function __construct(string $attributeName, string $attributeType, string $attributeCode, ?string
    $typeDesc, ?string $value = null)
    {
        $this->attributeName = $attributeName;
        $this->attributeType = $attributeType;
        $this->attributeCode = $attributeCode;
        $this->typeDesc = $typeDesc;
        $this->value = $value;
    }


}