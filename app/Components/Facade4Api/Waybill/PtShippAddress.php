<?php

namespace App\Components\Facade4Api\Waybill;

/**
 * 平台的发货地址
 */
class PtShippAddress
{
    /**
     * 省
     * @var string
     */
    public string $province;
    /**
     * 城市
     * @var string
     */
    public string $city;
    /**
     * 区
     * @var string
     */
    public string $district;
    /**
     * 详细地址
     * @var string
     */
    public string $detail;
    /**
     * 街道名称
     * @var string
     */
    public string $street;


    /**
     * 是否支持街道
     * @return bool
     */
    public function isStreetSupported():bool{
        return !empty($this->street);
    }

    /**
     * 获取地址字符串
     * @return string
     */
    public function getAddressString():string{
        if ($this->isStreetSupported()) {
            $tmpAddress = $this->province . ',' . $this->city . ',' .$this->district . ',' .
                $this->street. ',' . $this->detail;
        } else {
            $tmpAddress = $this->province . ',' . $this->city  . ','  .$this->district . ',' .$this->detail;

        }
        return $tmpAddress;
    }


}