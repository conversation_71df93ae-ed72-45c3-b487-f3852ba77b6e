<?php

namespace App\Components\Facade4Api\Waybill;

use App\Model\Package\PackageGood;

/**
 * 平台的包裹
 */
class FaPackage
{
    /**
     * 代表请求的ID
     * @var string
     */
    public string $requestId;
    /**
     * 包裹来源
     * @var string
     */
    public string $source;
    /**
     * 平台订单ID，合单打印的时候，选一个为主
     * @var string|null
     */
    public ?string $tid;

    /**
     * 加密地址ID
     * @var string|null
     */
    public ?string $addressMd5;

    /**
     * @var string|null 加密订单ID，主要是淘宝用的，对应于OAID和CAID
     */
    public ?string $encryptionId;

    /**
     * 应用自己的订单ID,合单打印的时候，选一个订单为主
     * @var int
     */
    public int $orderId;
    /**
     * 包裹的数量-主要是子母件用
     * @var int
     */
    public int $num;
    /**
     * 收件人
     * @var FaReceiver
     */
    public FaReceiver $receiver;
    /**
     * 包裹
     * @var FaPackageGood[]
     */
    public array $packageGoods;

    /**
     *  面单号，更新电子面单的时候会传过来
     * @var string|null
     */

    public ?string $waybillCode;

    /**
     * 包裹对于的店铺ID （如果是合单打印，这个值就是主订单的店铺ID，万一跨店铺合单，）
     * @var int|null
     */
    public ?int $shopId;

    public ?string $receiverName;
    public ?string $receiverProvince;
    public ?string $receiverCity;
    public ?string $receiverDistrict;
    public ?string $receiverTown;
    public ?string $receiverZip;
    public ?string $receiverAddress;
    public ?string $receiverPhone;
    public ?string $receiverPhoneIdx;
    public ?string $receiverTel;

    /**
     * 转成抖音取号的Items,更新面单和创建面单的Items是不一样的，这个方法只用于创建面单
     * @param FaPackage $package
     * @return array
     */
    public static function buildCreateWaybillDyItems(FaPackage $package): array
    {
        $items = [];
        if (isset($package->packageGoods)) {
            foreach ($package->packageGoods as $packageGood) {
                $temp = [];
                $temp['item_count'] = $packageGood->shippedNum;
                $temp['item_name'] = $packageGood->goodsTitle;
                $items[] = $temp;
            }
        }
        return $items;
    }


    /**
     * 转成KS的Items
     * @param FaPackage $package
     * @return array
     */
    public static function buildKsItems(FaPackage $package): array
    {
        $items = [];
        if (isset($package->packageGoods)) {
            foreach ($package->packageGoods as $packageGood) {
                $temp = [];
                $temp['itemQuantity'] = $packageGood->shippedNum;
                $temp['itemTitle'] = $packageGood->goodsTitle;
                $items[] = $temp;
            }
        }
        return $items;
    }

    /**
     * 转成Tb的Items
     * @param FaPackage $package
     * @return array
     */
    public static function buildTbItems(FaPackage $package): array
    {
        $items = [];
        if (isset($package->packageGoods)) {
            foreach ($package->packageGoods as $packageGood) {
                $temp = [];
                $temp['count'] = $packageGood->shippedNum;
                $temp['name'] = $packageGood->goodsTitle;
                $items[] = $temp;
            }
        }
        return $items;
    }
}