<?php

namespace App\Components\Facade4Api\Shop\Impl;

use App\Components\Facade4Api\Client\DyClient;
use App\Components\Facade4Api\Client\PddClient;
use App\Components\Facade4Api\Constants\SubscriptionConst;
use App\Components\Facade4Api\Exception\ApiException;
use App\Components\Facade4Api\Shop\AbstractShopFacade;
use App\Components\Facade4Api\Shop\FaShopInfo;
use App\Components\Facade4Api\Shop\ShopBindParam;
use App\Components\Facade4Api\Shop\ShopBindParamRequest;
use App\Components\Facade4Api\Shop\SubscriptionInfo;
use App\Components\Facade4Api\Shop\SubscriptionParam;
use App\Utils\DateTimeUtil;
use App\Utils\Log;
use GuzzleHttp\Exception\GuzzleException;
use Hyperf\Collection\Arr;

/**
 * Pdd订阅相关的服务
 */
class PddShopImpl extends AbstractShopFacade
{

    /**
     * @inheritDoc
     * @throws ApiException|GuzzleException
     */
    public function sendSubscriptionInfo(SubscriptionParam $subscriptionParam): ?SubscriptionInfo
    {

        $this->isTokenAvailable($subscriptionParam->accessToken);
        $pddClient = PddClient::newInstance("");
        $responseBody=$pddClient->execute("pdd.servicemarket.contract.search",
            ["mallId"=>$subscriptionParam->openShopId]);
        $response = $responseBody["servicemarket_contract_search_response"];
        Log::info("获取订阅信息",[$response]);
        //特殊情况，例如白名单店铺是没有订阅信息的
        $specValueList=Arr::get($response,"specValue.specValueList",null);
        if(!$specValueList){
            return null;
        }
        $versionSpecValueArr=array_filter($specValueList,function($item){
           return $item['specName']=="服务版本";
        });
        $versionSpecValue=array_pop($versionSpecValueArr);
        $versionDesc=$versionSpecValue['specValue']??'';
        Log::info("获取版本信息",[$versionSpecValue,$versionDesc]);
        $subscriptionInfo = new SubscriptionInfo();
        $subscriptionInfo->expireAt = DateTimeUtil::second2str($response['endAt']/1000);
        $subscriptionInfo->startAt=DateTimeUtil::second2str($response['startAt']/1000);
        $subscriptionInfo->version = SubscriptionConst::VERSION_DESC_MAP_ARR[$versionDesc]??'' ;
        $subscriptionInfo->versionDesc = $versionDesc;

        return $subscriptionInfo;

    }

    /**
     * @param string $accessToken
     * @param ShopBindParamRequest $shopBindParam
     * @return string|null
     * @throws ApiException
     * @throws GuzzleException
     */
    public function sendBindTicketInfo(string $accessToken, ShopBindParamRequest $shopBindParam): ?string
    {
        $pddClient = PddClient::newInstance($accessToken);
        $responseBody=$pddClient->execute("pdd.pop.mall.bind.ticket.get",
            ["external_uid"=>$shopBindParam->associatedId]);
        return Arr::get($responseBody,"response.ticket");
    }


    /**
     * @throws GuzzleException
     * @throws ApiException
     */
    public function sendShopBind(string $accessToken, ShopBindParam $shopBindParam): array
    {
        $pddClient = PddClient::newInstance($accessToken);
        $responseBody=$pddClient->execute("pdd.pop.mall.bind.token.get",
            ["external_uid"=>$shopBindParam->associatedId,
                "bind_code"=>$shopBindParam->bindCode]);
        return Arr::get($responseBody,"pop_auth_token_create_response");
    }

    public function sendShopInfo(string $accessToken): FaShopInfo
    {
        $pddClient = PddClient::newInstance($accessToken);
        $responseBody=$pddClient->execute("pdd.mall.info.get",[],"post");
        $mallInfo = $responseBody["mall_info_get_response"];
        $faShopInfo=new FaShopInfo();
        $faShopInfo->shopName= $mallInfo['mall_name'] ?? '';
        $faShopInfo->shopLogo=$mallInfo['logo'] ?? '';
//        $user->setAttribute("id", $tokenResponse['owner_id'] ?? '');
//        $user->setAttribute("nickname", $tokenResponse['owner_name'] ?? '');
//        $user->setAttribute("username", $mallInfo['owner_name'] ?? '');
//        $user->setAttribute("name", $mallInfo['mall_name'] ?? '');
//        $user->setAttribute("openUserId",  '');
//        $user->setAttribute("logo",  $mallInfo['logo'] ?? '');
        return $faShopInfo;
    }

    function isTokenAvailable(string $accessToken): bool
    {
       $this->sendShopInfo($accessToken);
       return true;
    }
}