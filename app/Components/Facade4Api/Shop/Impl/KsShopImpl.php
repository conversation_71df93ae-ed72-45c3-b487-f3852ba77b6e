<?php

namespace App\Components\Facade4Api\Shop\Impl;

use App\Components\Facade4Api\Client\KsClient;
use App\Components\Facade4Api\Constants\SubscriptionConst;
use App\Components\Facade4Api\Shop\AbstractShopFacade;
use App\Components\Facade4Api\Shop\FaShopInfo;
use App\Components\Facade4Api\Shop\SubscriptionInfo;
use App\Components\Facade4Api\Shop\SubscriptionParam;
use App\Models\UserExtra;
use Carbon\Carbon;

/**
 * 快手相关的订阅服务实现
 */
class KsShopImpl extends AbstractShopFacade
{

    public function sendSubscriptionInfo(SubscriptionParam $subscriptionParam ): ?SubscriptionInfo
    {
        $accessToken=$subscriptionParam->accessToken;
        $openUserId=$subscriptionParam->openUserId;
        $client = KsClient::newInstance($accessToken,$subscriptionParam->platform,$subscriptionParam->serviceCode);
        $params = [
            'buyerOpenId' => $openUserId
        ];
        $response = $client->execute('get', '/open/service/market/buyer/service/info', $params);

        $expired_at = !empty($response['data']['endTime']) ? date('Y-m-d H:i:s', $response['data']['endTime'] / 1000) :
            Carbon::now()->addDays(30)->toDateTimeString();
        if (empty($response['data']['inService'])) {
            $expired_at = null;
        }
        $subscriptionInfo=new SubscriptionInfo();
        $subscriptionInfo->version=$response['data']['packageName'] == "专业版" ?
            SubscriptionConst::VERSION_PROFESSIONAL : SubscriptionConst::VERSION_FREE;
        $subscriptionInfo->versionDesc=$response['data']['packageName'] ??
            SubscriptionConst::VERSION_MAP_ARR[SubscriptionConst::VERSION_FREE];
        $subscriptionInfo->expireAt=$expired_at;
        return $subscriptionInfo;
    }

    public function sendShopInfo(string $accessToken): FaShopInfo
    {
        $ksClient = KsClient::newInstance($accessToken);
        $data = $ksClient->execute('get', 'open/shop/info/get', []);
        $faShopInfo=new FaShopInfo();
        $faShopInfo->shopName=$data['data']['shopName'];
        return $faShopInfo;
    }

    function isTokenAvailable(string $accessToken): bool
    {
       $this->sendShopInfo($accessToken);
       return true;
    }
}