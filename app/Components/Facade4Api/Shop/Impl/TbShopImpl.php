<?php

namespace App\Components\Facade4Api\Shop\Impl;

use App\Components\Facade4Api\Client\DyClient;
use App\Components\Facade4Api\Client\TbClient;
use App\Components\Facade4Api\Constants\SubscriptionConst;
use App\Components\Facade4Api\Shop\AbstractShopFacade;
use App\Components\Facade4Api\Shop\FaShopInfo;
use App\Components\Facade4Api\Shop\SubscriptionInfo;
use App\Components\Facade4Api\Shop\SubscriptionParam;
use App\Utils\Log;
use TopClient\request\ShopSellerGetRequest;
use TopClient\request\VasSubscribeGetRequest;

/**
 * 淘宝订阅相关的服务
 */
class TbShopImpl extends AbstractShopFacade
{

    /**
     * @inheritDoc
     */
    public function sendSubscriptionInfo(SubscriptionParam $subscriptionParam): ?SubscriptionInfo
    {

        Log::info('查询订购信息', [$subscriptionParam]);
        $accessToken = $subscriptionParam->accessToken;
        $client = TbClient::newInstance($accessToken);

        //调用服务订阅查询的接口获取过期时间等等
        $name = $subscriptionParam->userName;
        $request = new VasSubscribeGetRequest();
        $request->setArticleCode(TbClient::getConfig("article_code", $subscriptionParam->platform,
            $subscriptionParam->serviceCode));
        $request->setNick($name);
        list($requestUrl, $apiParams) = $client->getApiParamsAndUrl($request);
        $response = $client->execute($requestUrl, $apiParams);
        $arr = $response['vas_subscribe_get_response']['article_user_subscribes'] ['article_user_subscribe'] ?? [];
        if (sizeof($arr) == 0) {
            return null;
        }
        $subscriptionInfo = new SubscriptionInfo();
        $subscriptionInfo->expireAt = $arr[0]['deadline'];
        $subscriptionInfo->version = (strtotime($arr[0]['deadline']) - time()) > 86400 * 7 ? 'Standard' : "Free";
        $subscriptionInfo->versionDesc = (strtotime($arr[0]['deadline']) - time()) > 86400 * 7 ? "标准" : "免费";
        return $subscriptionInfo;
    }

    function isTokenAvailable(string $accessToken): bool
    {
        $this->sendShopInfo($accessToken);
        return true;
    }

    public function sendShopInfo(string $accessToken): FaShopInfo
    {
        $client = TbClient::newInstance($accessToken);
        //先查一下店铺信息，看token是不是正确;
        $request = new ShopSellerGetRequest();
        $request->setFields("sid,title,pic_path");
        $response = $client->executeRequest($request, $accessToken);
        $shopInfo = $response['shop_seller_get_response']['shop'];
        $faShopInfo = new FaShopInfo();
        $faShopInfo->shopName = $shopInfo['title'];
        $faShopInfo->shopLogo = $shopInfo['pic_path'];
        return $faShopInfo;
    }
}