<?php

namespace App\Components\Facade4Api\Shop\Impl;

use App\Components\Facade4Api\Constants\SubscriptionConst;
use App\Components\Facade4Api\Shop\AbstractShopFacade;
use App\Components\Facade4Api\Shop\SubscriptionInfo;
use App\Components\Facade4Api\Shop\SubscriptionParam;
use Carbon\Carbon;

class XhsShopImpl extends AbstractShopFacade
{

    /**
     * @inheritDoc
     */
    public function sendSubscriptionInfo(SubscriptionParam $subscriptionParam): ?SubscriptionInfo
    {
//        $client = DyClient::newInstance($subscriptionParam->accessToken);
//        $data = [
//            'refresh_token' => $refreshToken,
//        ];
//        $response = $client->execute('oauth.refreshToken', $data);
//        $version = $response['data']['spec_val'] == "专业版" ? SubscriptionConst::VERSION_PROFESSIONAL :
//            ($response['data']['spec_val'] == "标准版" ? SubscriptionConst::VERSION_STANDARD : SubscriptionConst::VERSION_FREE);
        $version = SubscriptionConst::VERSION_STANDARD;
        // 先写死
        $subscriptionInfo = new SubscriptionInfo();
        $subscriptionInfo->expireAt = Carbon::now()->addDays(30)->toDateTimeString();
        $subscriptionInfo->version = $version;
        $subscriptionInfo->versionDesc = SubscriptionConst::VERSION_MAP_ARR[$version] ?? '';
        return $subscriptionInfo;
    }

    /**
     * @inheritDoc
     */
    function isTokenAvailable(string $accessToken): bool
    {
        $subscriptionParam=new SubscriptionParam();
        $subscriptionParam->accessToken = $accessToken;
        $this->sendSubscriptionInfo($subscriptionParam);
        return true;
    }
}