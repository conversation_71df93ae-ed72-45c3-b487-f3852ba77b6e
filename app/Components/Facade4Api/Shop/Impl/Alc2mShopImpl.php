<?php

namespace App\Components\Facade4Api\Shop\Impl;

use App\Components\Facade4Api\Client\AlbbClient;
use App\Components\Facade4Api\Constants\SubscriptionConst;
use App\Components\Facade4Api\Exception\ApiException;
use App\Components\Facade4Api\Shop\AbstractShopFacade;
use App\Components\Facade4Api\Shop\SubscriptionInfo;
use App\Components\Facade4Api\Shop\SubscriptionParam;
use App\Model\Subscription\Subscription;
use App\Response\ClientThrowable;
use App\Utils\DateTimeUtil;
use App\Utils\Log;
use GuzzleHttp\Exception\GuzzleException;
use Hyperf\Collection\Arr;

/**
 * 阿里巴巴订阅相关的服务
 */
class Alc2mShopImpl extends AbstractShopFacade
{

    /**
     * @param SubscriptionParam $subscriptionParam
     * @return SubscriptionInfo|null
     */
    public function sendSubscriptionInfo(SubscriptionParam $subscriptionParam): ?SubscriptionInfo
    {
        $subscriptionInfo = new SubscriptionInfo();
        $subscriptionInfo->startAt ='';
        $subscriptionInfo->expireAt = $subscriptionParam->authExpireAt??'';
        $subscriptionInfo->version=SubscriptionConst::VERSION_PROFESSIONAL;
        $subscriptionInfo->versionDesc=SubscriptionConst::VERSION_PROFESSIONAL_DESC;
        return $subscriptionInfo;

    }

    function isTokenAvailable(string $accessToken): bool
    {
        // TODO: Implement isTokenAvailable() method.
        return true;
    }
}