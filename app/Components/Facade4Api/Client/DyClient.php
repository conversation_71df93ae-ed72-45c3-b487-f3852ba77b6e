<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/6/9
 * Time: 19:41
 */

namespace App\Components\Facade4Api\Client;


use App\Components\Facade4Api\Constants\PlatformConst;
use App\Components\Facade4Api\Exception\ApiException;
use App\Components\Facade4Api\Exception\ErrorConstants;
use App\Components\Facade4Api\Util\Convertor;
use App\Response\ClientThrowable;
use App\Utils\Log;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use JetBrains\PhpStorm\ArrayShape;
use Throwable;

class DyClient extends AbstractClient
{
    protected string $appkey;

    protected string $secretKey;

    protected string $gatewayUrl = "https://openapi-fxg.jinritemai.com";

    protected string $platform = PlatformConst::DY;

    protected $apiVersion = "2";
    /**
     * @var mixed
     */
    private string $accessToken = '';
    /**
     * 默认超时时间 秒
     * @var int
     */
    private int $clientTimeout = 8;
    private int $clientConnectTimeout = 3; // 客户端连接超时

    /**
     * 最大重试次数
     */
    const MAX_RETRIES = 3;

    /**
     * @var Client
     */
    protected Client $client;

    public static function newInstance($accessToken, $platform = null, $serviceCode = null): DyClient
    {
        empty($platform) && $platform = PlatformConst::DY;
        $client = new DyClient(self::getConfig("client_id", $platform, $serviceCode), self::getConfig("client_secret",
            $platform, $serviceCode
        ));
        $client->setAccessToken($accessToken);
        return $client;
    }


    /**
     * 执行请求
     * @param string $apiMethod 接口名 例如：product/getGoodsCategory
     * @param array $apiParams 接口参数
     * @param string $requestMethod
     * @return array
     * @throws ApiException
     * @throws GuzzleException|ClientThrowable
     * <AUTHOR>
     */
    public function execute(string $apiMethod, array $apiParams, string $requestMethod = 'get'): array
    {
        $request_data = $this->buildRequestData($apiParams, $apiMethod);
        $base_url = $this->getApiFullUrl($apiMethod);

        $httpClient = $this->getHttpClient();
        $requestMethod = strtolower($requestMethod);
        if ($requestMethod == 'get') {
            $dataType = 'query';
        } else {
            $dataType = 'form_params';
        }
        $response = $httpClient->$requestMethod($base_url, [
            $dataType => $request_data,
        ]);
        return $this->handleResponse($response->getBody(), compact('apiMethod', 'apiParams'));
    }



//    /**
//     * 刷新token
//     * @param string $refreshToken
//     * @return array
//     * @throws ClientException
//     */
//    public function refreshToken(string $refreshToken)
//    {
//
//        $data = [
//            'refresh_token' => $refreshToken,
//            'grant_type' => 'refresh_token',
//        ];
//        $appKey = config('socialite.dy.client_id');
//        $secretKey = config('socialite.dy.client_secret');
//        $client = new DyClient($appKey, $secretKey);
//
//        $params = $client->buildRequestData($data, 'token.refresh');
//        $httpClient = $this->getHttpClient();
//        $response = $httpClient->get($this->gatewayUrl . '/token/refresh', [
//            'query' => $params,
//            'headers' => [
//                'Content-type' => 'application/json',
//                "Accept" => "application/json"
//            ],
//        ]);
//        return $this->handleResponse($response->getBody());
//    }

    /**
     * 生成签名
     * @param $method
     * @param $params
     * @param $timestamp
     * @return string
     */
    private function generateSign($method, $params, $timestamp): string
    {
        ksort($params);
        //不转义中文(转义后会导致签名错误)
        if (in_array($method, ['order.addOrderRemark', 'logistics.createSFOrder', 'logistics.newCreateOrder', 'logistics.updateOrder', 'order.batchEncrypt', 'order.getSearchIndex'])) {
            $param_json = json_encode($params, JSON_UNESCAPED_UNICODE);
        } else if ($method == 'logistics.getShopKey' || $method == 'rights.info' || empty($params)) {
            $param_json = '{}';
        } else {
            $param_json = json_encode($params, JSON_UNESCAPED_UNICODE);
        }

        // 计算签名
        $str = "app_key" . $this->appkey . "method" . $method . "param_json" . $param_json . "timestamp" . $timestamp . "v" . $this->apiVersion;
        $md5_str = $this->secretKey . $str . $this->secretKey;
        if (in_array($method, ['logistics.createSFOrder', 'logistics.newCreateOrder', 'logistics.updateOrder'])) {
            $md5 = md5(str_replace('\\/', '/', $md5_str));
        } else if ($method == 'order.batchSensitive' || $method == 'order.batchDecrypt' || $method == 'order.batchEncrypt') {
            $md5 = md5(str_replace('\\/', '/', $md5_str));
        } else {
            $md5 = md5($md5_str);
        }
        return $md5;
    }

    /**
     * @return Client
     * <AUTHOR>
     */
    public function getHttpClient(): Client
    {
        return parent::getHttpClient();
    }


    /**
     * @param mixed $accessToken
     * @return DyClient
     */
    public function setAccessToken($accessToken)
    {
        $this->accessToken = $accessToken;
        return $this;
    }


    /**
     * @param array $apiParams
     * @param string $apiMethod
     * @return array
     * <AUTHOR>
     */
    #[ArrayShape(['app_key' => "string", 'method' => "array|string|string[]", 'access_token' => "mixed|string",
        'param_json' => "false|string", 'timestamp' => "string", 'v' => "string", 'sign' => "string"])]
    public function buildRequestData(array $apiParams, string $apiMethod): array
    {
        $apiParams = array_map(function ($item) {
            return Convertor::toApiParam($item);
        }, $apiParams);
        ksort($apiParams);
        $method = str_replace('/', '.', $apiMethod);
        $timestamp = date('Y-m-d H:i:s', time());


        // 构造请求url
        $sign = $this->generateSign($method, $apiParams, $timestamp);
        return [
            'app_key' => $this->appkey,
            'method' => $method,
            'access_token' => $this->accessToken,
            'param_json' => empty($apiParams) ? '{}' : json_encode($apiParams, JSON_UNESCAPED_UNICODE),
            'timestamp' => $timestamp,
            'v' => $this->apiVersion,
            'sign' => $sign,
        ];
    }


    /**
     * 处理抖音响应的错误
     * @see https://op.jinritemai.com/docs/guide-docs/10/23
     * <AUTHOR>
     * @param $body
     * @param array $request
     * @throws ApiException
     */
    public function handleErrorCode($body, array $request = []): void
    {
        if (is_object($body)) {
            $body = json_decode(json_encode($body, JSON_UNESCAPED_UNICODE), true);
        }
        if (!array_key_exists('code', $body)) {
            throw new ApiException(ErrorConstants::PLATFORM_SERVER_ERROR);
        }
        if ($body['code'] == 10000) {
            return;
        }
        Log::warning("平台返回错误", [$body]);
        $subCode = $body['sub_code'];
        switch ($subCode) {
            // 错误码 @see https://op.jinritemai.com/docs/guide-docs/212/1427
            case 'dop.authorization-no-existed':
            case 'dop.authorization-closed':
                throw new ApiException(ErrorConstants::PLATFORM_SHOP_AUTH_CANCELED);
            case 'isv.access-token-expired':
            case 'isv.access-token-no-existed':
                throw new ApiException(ErrorConstants::PLATFORM_SHOP_AUTH_EXPIRED);
            case 'isv.traffic-limited':
                throw new ApiException(ErrorConstants::PLATFORM_REQUEST_LIMIT);
            case 'isv.env-suspected':
                throw new ApiException(ErrorConstants::PLATFORM_ANTISPAM);
            default:
                $subMsg = $body['sub_msg'] ?? null;
                $msg=$body['msg'] ?? '';
                if(isset($subMsg)){
                    $msg=$msg.':'.$subMsg;
                }
                Log::warning("抖音平台错误:",$body);
                throw new ApiException([ErrorConstants::PLATFORM_SERVER_ERROR[0], $msg],$subCode);
        }
    }

    /**
     * retryDecider
     * 返回一个匿名函数, 匿名函数若返回false 表示不重试，反之则表示继续重试
     * @return \Closure
     */
    protected function retryDecider()
    {
        return function (
            $retries,
            Request $request,
            Response $response = null,
            Throwable $exception = null
        ) {
            // 超过最大重试次数，不再重试
            if ($retries >= self::MAX_RETRIES) {
                return false;
            }

            if ($response) {
                parse_str($request->getUri()->getQuery(), $params);
                if (!empty($params)) {
                    $method = $params['method'] ?? "";
                    //发货失败请求重试
                    if (in_array($method, ['order.logisticsAdd'])) {
                        // 请求失败，继续重试
                        if ($exception instanceof ConnectException) {
                            return true;
                        }
                        $body = !is_array($response->getBody()) ? json_decode($response->getBody(), true) : $response->getBody();
                        // dy 错误码汇总 https://op.jinritemai.com/docs/guide-docs/161/1427
                        if (isset($body['code']) && in_array($body['code'], [20000, 60000])) {
                            return true;
                        }
                    }
                }
            }

            return false;
        };
    }

    /**
     * 返回一个匿名函数，该匿名函数返回下次重试的时间（毫秒）
     * @return \Closure
     */
    protected function retryDelay()
    {
        return function ($numberOfRetries) {
            return 1000 * $numberOfRetries;
        };
    }

    public function executeRequest($request, $accessToken)
    {
        return $this->execute($request->method, $request->params, $request->requestMethod);
    }
}
