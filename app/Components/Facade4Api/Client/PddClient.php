<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/6/9
 * Time: 19:41
 */

namespace App\Components\Facade4Api\Client;


use App\Components\Facade4Api\Constants\PlatformConst;
use App\Components\Facade4Api\Exception\ApiException;
use App\Components\Facade4Api\Exception\ErrorConstants;
use App\Response\ClientThrowable;
use App\Utils\Log;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use Throwable;

class PddClient extends AbstractClient
{
    protected string $appkey;

    protected string $secretKey;

    protected string $gatewayUrl = "https://gw-api.pinduoduo.com/api/router";

    protected string $platform = PlatformConst::PDD;

    public string $dataType = "JSON";

    public string $apiVersion = "V1";
    /**
     * @var mixed
     */
    private string $accessToken = '';
    /**
     * 默认超时时间 秒
     * @var int
     */
    private int $clientTimeout = 8;
    private int $clientConnectTimeout = 3; // 客户端连接超时

    /**
     * 最大重试次数
     */
    const MAX_RETRIES = 3;

    /**
     * @var Client
     */
    protected Client $client;

    public static function newInstance(string $accessToken, $platform = null, $serviceCode = null): PddClient
    {
        empty($platform) && $platform = PlatformConst::PDD;
        $client = new PddClient(
            self::getConfig("client_id", $platform, $serviceCode),
            self::getConfig("client_secret", $platform, $serviceCode)
        );
        $client->setAccessToken($accessToken);
        return $client;
    }

    public function __construct($appKey, $secretKey, $timeout = 8)
    {
        $this->appkey = $appKey;
        $this->secretKey = $secretKey;
        $this->clientTimeout = $timeout;
    }

    /**
     * 执行请求
     * @param string $apiMethod 接口名 例如：product/getGoodsCategory
     * @param array $apiParams 接口参数
     * @param string $requestMethod
     * @return array
     * @throws ApiException
     * @throws GuzzleException
     * @throws ClientThrowable
     * <AUTHOR>
     */
    public function execute(string $apiMethod, array $apiParams, string $requestMethod = 'get'): array
    {
//        $sysParams = [];
//        $sysParams['client_id'] = $this->appkey;
//        if (!empty($this->accessToken)) {
//            $sysParams['access_token'] = $this->accessToken;
//        }
//        $sysParams['data_type'] = $this->dataType;
//        $sysParams['version'] = $this->apiVersion;
//        $sysParams['timestamp'] = time();
//        $sysParams['type'] = $apiMethod;
//        $sysParams['sign'] = $this->generateSign($this->secretKey, array_merge($sysParams, $apiParams));
//        $params = array_merge($sysParams, $apiParams);
        $params = $this->buildRequestData($apiParams, $apiMethod);

        $response = $this->getHttpClient()->post($this->gatewayUrl, [
            'query' => $params,
            'headers' => [
                'Content-type' => 'application/json',
                "Accept" => "application/json"
            ],
        ]);
        return $this->handleResponse($response->getBody(), compact('apiMethod', 'apiParams'));
    }

    /**
     * 生成签名
     * @param $appSecret
     * @param $params
     * @return string
     */
    private function generateSign($appSecret, $params): string
    {
        ksort($params);
        $sign = $appSecret;

        foreach ($params as $k => $v) {
            if (!str_starts_with($v, "@")) {
                $sign .= "$k$v";
            }
        }
        unset($k, $v);
        $sign .= $appSecret;
        return strtoupper(md5($sign));
    }

    /**
     * @return Client
     * <AUTHOR>
     */
    public function getHttpClient(): Client
    {
        return parent::getHttpClient();
    }

    /**
     * @param mixed $accessToken
     * @return PddClient
     */
    public function setAccessToken($accessToken)
    {
        $this->accessToken = $accessToken;
        return $this;
    }


    /**
     * 处理PDD响应的错误
     * @see https://op.jinritemai.com/docs/guide-docs/10/23
     * <AUTHOR>
     * @param $body
     * @param array $request
     * @throws ApiException
     */
    public function handleErrorCode($body,array  $request = []): void
    {
        if (is_object($body)) {
            $body = json_decode(json_encode($body, JSON_UNESCAPED_UNICODE), true);
        }elseif(is_string($body)){
            $body = json_decode($body, true);
        }
        if (!array_key_exists('error_response', $body)) {
            return;
        }
        $error_response = $body['error_response'];
        Log::debug("拼多多平台返回错误", [$body]);
        $subCode = $error_response['sub_code'];
        $errorCode = $error_response['error_code'];
        $subMsg = $error_response['sub_msg'] ?? null;
        $msg = $error_response['error_msg'] ?? '';
        switch ($subCode) {
            case 10019: // {"error_msg":"access_token已过期","sub_msg":"access_token已过期","sub_code":"10019","error_code":10019,
                throw new ApiException(ErrorConstants::PLATFORM_SHOP_AUTH_EXPIRED);
            default:
                if (isset($subMsg)) {
                    $msg = $msg . ';' . $subMsg;
                }
                Log::warning("拼多多平台错误:", $body);
                ApiException::throwPlatformException('拼多多平台错误:'.$msg, $errorCode, $subCode);
//                throw new ApiException([ErrorConstants::PLATFORM_SERVER_ERROR[0], '拼多多平台错误:'.$msg], $subCode);
        }
    }

    /**
     * 构建请求数据
     * @param array $apiParams
     * @param string $apiMethod
     * @return array
     */
    public function buildRequestData(array $apiParams, string $apiMethod): array
    {
        $sysParams = [];
        $sysParams['client_id'] = $this->appkey;
        if (!empty($this->accessToken)) {
            $sysParams['access_token'] = $this->accessToken;
        }
        $sysParams['data_type'] = $this->dataType;
        $sysParams['version'] = $this->apiVersion;
        $sysParams['timestamp'] = time();
        $sysParams['type'] = $apiMethod;
        $sysParams['sign'] = $this->generateSign($this->secretKey, array_merge($sysParams, $apiParams));
        return array_merge($sysParams, $apiParams);
    }

    /**
     * retryDecider
     * 返回一个匿名函数, 匿名函数若返回false 表示不重试，反之则表示继续重试
     * @return \Closure
     */
    protected function retryDecider()
    {
        return function (
            $retries,
            Request $request,
            Response $response = null,
            Throwable $exception = null
        ) {
            // 超过最大重试次数，不再重试
            if ($retries >= self::MAX_RETRIES) {
                return false;
            }

            if ($response) {
                parse_str($request->getUri()->getQuery(), $params);
                if (!empty($params)) {
                    $method = $params['method'] ?? "";
                    //发货失败请求重试
                    if (in_array($method, ['order.logisticsAdd'])) {
                        // 请求失败，继续重试
                        if ($exception instanceof ConnectException) {
                            return true;
                        }
                        $body = !is_array($response->getBody()) ? json_decode($response->getBody(), true) : $response->getBody();
                        // dy 错误码汇总 https://op.jinritemai.com/docs/guide-docs/161/1427
                        if (isset($body['code']) && in_array($body['code'], [20000, 60000])) {
                            return true;
                        }
                    }
                }
            }

            return false;
        };
    }

    /**
     * 返回一个匿名函数，该匿名函数返回下次重试的时间（毫秒）
     * @return \Closure
     */
    protected function retryDelay()
    {
        return function ($numberOfRetries) {
            return 1000 * $numberOfRetries;
        };
    }

    public function executeRequest($request, $accessToken)
    {
        $this->setAccessToken($accessToken);
        return $this->execute($request->method, $request->params, $request->requestMethod);
    }
}
