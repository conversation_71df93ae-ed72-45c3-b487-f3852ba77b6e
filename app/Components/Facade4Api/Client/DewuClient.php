<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/6/9
 * Time: 19:41
 */

namespace App\Components\Facade4Api\Client;


use App\Components\Facade4Api\Constants\PlatformConst;
use App\Components\Facade4Api\Exception\ApiException;
use App\Components\Facade4Api\Exception\ErrorConstants;
use App\Response\ClientThrowable;
use App\Utils\Log;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;

class DewuClient extends AbstractClient
{
    protected string $appkey;

    protected string $secretKey;

    protected string $gatewayUrl = "https://openapi.dewu.com";

    protected string $platform = PlatformConst::DEWU;
//    public string $gatewayUrl = "https://openapi-sandbox.dewu.com";

    public string $dataType = "JSON";

    public string $apiVersion = "V1";
    /**
     * @var mixed
     */
    private string $accessToken = '';
    /**
     * 默认超时时间 秒
     * @var int
     */
    private int $clientTimeout = 8;
    private int $clientConnectTimeout = 3; // 客户端连接超时

    /**
     * 最大重试次数
     */
    const MAX_RETRIES = 3;

    /**
     * @var Client
     */
    protected Client $client;

    public static function newInstance(string $accessToken, $platform = null, $serviceCode = null): DewuClient
    {
        empty($platform) && $platform = PlatformConst::DEWU;
        $client = new DewuClient(
            self::getConfig("client_id", $platform, $serviceCode),
            self::getConfig("client_secret", $platform, $serviceCode)
        );
        $client->setAccessToken($accessToken);
        return $client;
    }

    public function __construct($appKey, $secretKey, $timeout = 8)
    {
        $this->appkey = $appKey;
        $this->secretKey = $secretKey;
        $this->clientTimeout = $timeout;
    }

    /**
     * 执行请求
     * @param string $apiMethod 接口名 例如：product/getGoodsCategory
     * @param array $apiParams 接口参数
     * @param string $requestMethod
     * @return array
     * @throws ApiException
     * @throws GuzzleException
     * @throws ClientThrowable
     * <AUTHOR>
     */
    public function execute(string $apiMethod, array $apiParams, string $requestMethod = 'get'): array
    {
        $params = $this->buildRequestData($apiParams, $apiMethod);

        $requestMethod = strtoupper($requestMethod);
        $paramType = $requestMethod == 'GET' ? 'query' : 'json';
        $response = $this->getHttpClient()->request($requestMethod, $this->gatewayUrl . $apiMethod, [
            $paramType => $params,
            'headers' => [
                'Content-type' => 'application/json',
                "Accept" => "application/json"
            ],
        ]);
        return $this->handleResponse($response->getBody(), compact('apiMethod', 'apiParams'));
    }

    /**
     * 生成签名
     * @param $appSecret
     * @param $params
     * @return string
     */
    private function generateSign($paramArr): string
    {
        ksort($paramArr);
        foreach($paramArr as $key => $val) { //过滤空数据项
            if(is_array($val)) {
                $paramArr[$key] = json_encode($val, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            }
        }
        $sign_str = http_build_query($paramArr, NULL, '&');
        $sign_str .= $this->secretKey;
        return strtoupper(md5($sign_str));
    }

    /**
     * @return Client
     * <AUTHOR>
     */
    public function getHttpClient(): Client
    {
        return parent::getHttpClient();
    }

    /**
     * @param mixed $accessToken
     * @return PddClient
     */
    public function setAccessToken($accessToken)
    {
        $this->accessToken = $accessToken;
        return $this;
    }


    /**
     * 处理PDD响应的错误
     * @see https://op.jinritemai.com/docs/guide-docs/10/23
     * <AUTHOR>
     * @param $body
     * @param array $request
     * @throws ApiException
     */
    public function handleErrorCode($body, array $request = []): void
    {
        if (is_object($body)) {
            $body = json_decode(json_encode($body, JSON_UNESCAPED_UNICODE), true);
        }elseif(is_string($body)){
            $body = json_decode($body, true);
        }
        $code = $body['code'];
        $msg = $body['msg'];
        switch ($code) {
            case 200:
                return;
            case '?': // {"error_msg":"access_token已过期","sub_msg":"access_token已过期","sub_code":"10019","error_code":10019,
                throw new ApiException(ErrorConstants::PLATFORM_SHOP_AUTH_EXPIRED);
            default:
                Log::warning("得物平台错误:", $body);
                ApiException::throwPlatformException('得物平台错误:'.$msg."[$code]");
        }
    }

    /**
     * 构建请求数据
     * @param array $apiParams
     * @param string $apiMethod
     * @return array
     */
    public function buildRequestData(array $apiParams, string $apiMethod): array
    {
        $sysParams = [];
        $sysParams['app_key'] = $this->appkey;
        if (!empty($this->accessToken)) {
            $sysParams['access_token'] = $this->accessToken;
        }
        $sysParams['timestamp'] = time() * 1000;
        $sysParams['sign'] = $this->generateSign(array_merge($sysParams, $apiParams));
        return array_merge($sysParams, $apiParams);
    }


    public function executeRequest($request, $accessToken)
    {
        $this->setAccessToken($accessToken);
        return $this->execute($request->method, $request->params, $request->requestMethod);
    }
}
