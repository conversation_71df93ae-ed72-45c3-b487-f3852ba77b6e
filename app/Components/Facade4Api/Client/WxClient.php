<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/3/18
 * Time: 20:59
 */

namespace App\Components\Facade4Api\Client;


use App\Components\Facade4Api\Constants\PlatformConst;
use App\Components\Facade4Api\Exception\ApiException;
use App\Components\Facade4Api\Exception\ErrorConstants;
use App\Utils\Log;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use Hyperf\Redis\Redis;
use Hyperf\Redis\RedisFactory;
use Hyperf\Utils\ApplicationContext;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use Psr\Http\Message\StreamInterface;
use Psr\SimpleCache\InvalidArgumentException;

/**
 * 微信客户端
 */
class WxClient extends AbstractClient
{
    const COMPONENT_VERIFY_TICKET_REDIS_KEY = 'component_verify_ticket';
    protected string $appkey;
    protected string $secretKey;
    protected string $gatewayUrl = "https://api.weixin.qq.com";
    protected string $platform = PlatformConst::WX;
    protected string $appKey;
    private ?string $accessToken;
    protected ?string $serviceId;
    protected ?string $specificationId;
    public string $authUrl = "https://api.weixin.qq.com";

    /**
     * 最大重试次数
     */
    const MAX_RETRIES = 3;

    public function __construct($appKey, $secretKey)
    {
        $this->appkey = $appKey;
        $this->secretKey = $secretKey;
        $this->appKey = $appKey;
    }

    /**
     * @return Client
     * <AUTHOR>
     */
    public function getHttpClient(): Client
    {
        return parent::getHttpClient();
    }

    /**
     * 客户端创建实例
     * @param $accessToken
     * @param $serviceId
     * @param $specificationId
     * @param $platform
     * @param $serviceCode
     * @return WxClient
     */

    public static function newInstance($accessToken, $serviceId, $specificationId,
                                       $platform, $serviceCode)
    {
        empty($platform) && $platform = PlatformConst::WX;
        $client = new WxClient(self::getConfig('client_id', $platform, $serviceCode), self::getConfig('client_secret', $platform,
            $serviceCode));
        $client->setAccessToken($accessToken);
        $client->setServiceId($serviceId);
        $client->setSpecificationId($specificationId);
        return $client;
    }

    /**
     * @param $method
     * @param $url
     * @param array $params
     * @return array
     * <AUTHOR>
     */
    public function execute($method, $url, $params = [])
    {
        $httpClient = $this->getHttpClient();

        //post和get方式处理
        $headers = [
            'Content-type' => 'application/json',
            "Accept" => "application/json"
        ];
        switch ($method) {
            case 'get':
                $postKey = 'query';
                break;
            case 'post':
                $postKey = 'json';
                break;
            default:
                $postKey = 'form_params';
                $headers = [];
                break;
        }

        $validParams = [
            'access_token' => $this->accessToken,
            'service_id' => $this->serviceId,
            'specification_id' => $this->specificationId,
        ];
        $applyUrl = $this->gatewayUrl . $url . '?' . http_build_query($validParams);

        $response = $httpClient->request($method, $applyUrl, [
            $postKey => $params,
            'headers' => $headers,
        ]);

        //	\Log::debug('aaaaaaaaaaaaaaaaaaaaaaaaaaa', [
        //		'method'  => $method,
        //		'url'     => $applyUrl,
        //		'params'  => $params,
        //		'postKey' => $postKey,
        //		'headers' => $headers,
        //		    'response' => $this->handleResponse($response->getBody()),
        //	]);

        return $this->handleResponse($response->getBody());
    }

    /**
     * 获取缓存
     * @return mixed|\Psr\SimpleCache\CacheInterface
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function getCache(): mixed
    {
        $container = ApplicationContext::getContainer();
        return $container->get(\Psr\SimpleCache\CacheInterface::class);
    }

    /**
     * 获取redis
     * @return Redis
     * @throws NotFoundExceptionInterface
     * @throws ContainerExceptionInterface
     */
    public static function getRedis(): Redis
    {
        $container = ApplicationContext::getContainer();
        return $container->get(RedisFactory::class)->get('wx');
    }

    /**
     * @throws NotFoundExceptionInterface
     * @throws ApiException
     * @throws ContainerExceptionInterface
     * @throws GuzzleException
     * @throws InvalidArgumentException|\RedisException
     */
    public function getComponentAccessTokenFromCache()
    {
        $cache = $this->getCache();
        $componentAccessTokenKey = 'component_access_token';
        $componentAccessToken = $cache->get($componentAccessTokenKey, null);
        if (isset($componentAccessToken)) {
            Log::debug("从缓存中获取componentAccessToken");
            return $componentAccessToken;
        }
        $redis = self::getRedis();
         ;
        if (!$redis->exists(self::COMPONENT_VERIFY_TICKET_REDIS_KEY)) {
            $errorMsg = '缺少微信服务器参数:component_verify_ticket';
            Log::error($errorMsg);
            throw new \InvalidArgumentException($errorMsg);
        }
        $ticket = $redis->get(self::COMPONENT_VERIFY_TICKET_REDIS_KEY);
        Log::info("获取component_verify_ticket=" . $ticket);
        $componentAccessTokenResponse = $this->getComponentAccessTokenFromRemote($ticket);
        $expiresIn =15;// $componentAccessTokenResponse['expires_in']; //因为线上有两个应用，这个地方先用15秒限定一下
        $componentAccessToken = $componentAccessTokenResponse['component_access_token'];
        //缓存componentAccessToken
        $cache->set($componentAccessTokenKey, $componentAccessToken, $expiresIn);
        return $componentAccessToken;
    }


    /**
     * @throws GuzzleException
     * @throws ApiException
     */
    public function getComponentAccessTokenFromRemote(string $ticket = ''): StreamInterface|array
    {
        $params = [
            'component_appid' => $this->appKey,
            'component_appsecret' => $this->secretKey,
            self::COMPONENT_VERIFY_TICKET_REDIS_KEY => $ticket,
        ];
        Log::info('getComponentAccessToken params', [$params]);
        $httpClient = $this->getHttpClient();
        $response = $httpClient->post($this->authUrl . '/cgi-bin/component/api_component_token', [
            'json' => $params,
            'headers' => [
                'Content-type' => 'application/json',
                "Accept" => "application/json"
            ],
        ]);
        Log::info("ComponentAccessToken", [$response->getBody()]);
        return $this->handleResponse($response->getBody());

    }

    /**
     * 获取商家的服务ID
     * @param string $code
     * @return array|StreamInterface
     * @throws ApiException
     * @throws ContainerExceptionInterface
     * @throws GuzzleException
     * @throws NotFoundExceptionInterface
     * @throws InvalidArgumentException
     */
    public function getService(string $code): StreamInterface|array
    {
        $componentAccessToken=$this->getComponentAccessTokenFromCache();
        $params = [
            'code' => $code,
        ];
        Log::debug('getService params', [$params, $componentAccessToken]);
        $httpClient = $this->getHttpClient();
        $response = $httpClient->post($this->authUrl . '/product/service/check_auth?component_access_token=' .
            $componentAccessToken, [
            'json' => $params,
            'headers' => [
                'Content-type' => 'application/json',
                "Accept" => "application/json"
            ],
        ]);

        $service = $this->handleResponse($response->getBody());
        Log::debug("商家的服务信息", [$service]);
        return $service;
    }

    /**
     * 获取授权信息
     * @param string $authorizerAppid
     * @return array|StreamInterface
     * @throws ApiException
     * @throws ContainerExceptionInterface
     * @throws GuzzleException
     * @throws NotFoundExceptionInterface
     * @throws InvalidArgumentException
     */
    public function getAuthInfo( string $authorizerAppid): StreamInterface|array
    {
        $componentAccessToken=$this->getComponentAccessTokenFromCache();
        $params = [
            'component_appid' => $this->appKey,
            'authorizer_appid' => $authorizerAppid,
        ];
        Log::debug('getService params', [$params]);
        $httpClient = $this->getHttpClient();
        $response = $httpClient->post($this->authUrl .
            '/cgi-bin/component/api_get_authorizer_info?component_access_token=' .
            $componentAccessToken, [
            'json' => $params,
            'headers' => [
                'Content-type' => 'application/json',
                "Accept" => "application/json"
            ],
        ]);

        $authInfo = $response->getBody();
        Log::debug("授权信息", [$authInfo]);
        return $this->handleResponse($authInfo);
    }

    /**
     * 获取accessToken
     * @param string $authorizerAppid
     * @param string $authorizerRefreshToken
     * @return array|StreamInterface
     * @throws ApiException
     * @throws ContainerExceptionInterface
     * @throws GuzzleException
     * @throws InvalidArgumentException
     * @throws NotFoundExceptionInterface
     */
    public function getAccessToken(string $authorizerAppid, string $authorizerRefreshToken): StreamInterface|array

    {
        $componentAccessToken=$this->getComponentAccessTokenFromCache();
        $params = [
            'component_appid' => $this->appKey,
            'authorizer_appid' => $authorizerAppid,
            'authorizer_refresh_token' => $authorizerRefreshToken,
        ];
        Log::debug('getAccessToken params', [$params]);
        $httpClient = $this->getHttpClient();
        $response = $httpClient->post($this->authUrl .
            '/cgi-bin/component/api_authorizer_token?component_access_token=' .
            $componentAccessToken, [
            'json' => $params,
            'headers' => [
                'Content-type' => 'application/json',
                "Accept" => "application/json"
            ],
        ]);

        return $this->handleResponse($response->getBody());
    }

    /**
     * @param mixed $accessToken
     * @return WxClient
     */
    public function setAccessToken(?string $accessToken): WxClient
    {
        $this->accessToken = $accessToken;
        return $this;
    }

    /**
     * @param mixed $serviceId
     * @return WxClient
     */
    public function setServiceId(?string $serviceId): WxClient
    {
        $this->serviceId = $serviceId;
        return $this;
    }

    /**
     * @param mixed $specificationId
     * @return WxClient
     */
    public function setSpecificationId(?string $specificationId): WxClient
    {
        $this->specificationId = $specificationId;
        return $this;
    }


    public function getPreAuthCode($componentAccessToken)
    {
        $params = [
            'component_appid' => $this->appKey,
        ];
        Log::debug('getPreAuthCode params', [$params]);
        $httpClient = $this->getHttpClient();
        $response = $httpClient->post($this->authUrl . '/cgi-bin/component/api_create_preauthcode?component_access_token=' . $componentAccessToken, [
            'json' => $params,
            'headers' => [
                'Content-type' => 'application/json',
                "Accept" => "application/json"
            ],
        ]);

        return $this->handleResponse($response->getBody());
    }


    public function handleErrorCode($body,array  $request = []): void
    {
        if (is_string($body)) {
            $body = json_decode($body, true);
        }
        if (isset($body['errcode']) && $body['errcode'] != 0) {
            Log::error(get_class($this) . ' request error:' . $body['errmsg'], $body);
            switch ($body['errcode']) {
                case 109001:
                    throw new ApiException(ErrorConstants::ORDER_DELIVERED);
                case 40001:
                case 42001:
                    throw new ApiException(ErrorConstants::PLATFORM_SHOP_AUTH_EXPIRED);
                default:
                    Log::error("平台返回错误", $body);
                    $error = [ErrorConstants::PLATFORM_SERVER_ERROR[0], $body['errmsg']];
                    throw new ApiException($error);
//                    throw new OrderException('微信服务异常：' . json_encode($body, JSON_UNESCAPED_UNICODE));
            }
        }
    }

    /**
     * retryDecider
     * 返回一个匿名函数, 匿名函数若返回false 表示不重试，反之则表示继续重试
     * @return \Closure
     */
    protected function retryDecider()
    {
        return function (
            $retries,
            Request $request,
            Response $response = null,
            RequestException $exception = null
        ) {
            // 超过最大重试次数，不再重试
            if ($retries >= self::MAX_RETRIES) {
                return false;
            }

            if ($response) {
                parse_str($request->getUri()->getQuery(), $params);
                if (!empty($params)) {
                    $method = $params['method'] ?? "";
                    //发货失败请求重试
                    if (in_array($method, ['taobao.logistics.offline.send'])) {
                        // 请求失败，继续重试
                        if ($exception instanceof ConnectException) {
                            return true;
                        }
                        $body = !is_array($response->getBody()) ? json_decode($response->getBody(), true) : $response->getBody();
                        // todo 收集wx错误码进行重试
                    }
                }
            }

            return false;
        };
    }

    /**
     * 返回一个匿名函数，该匿名函数返回下次重试的时间（毫秒）
     * @return \Closure
     */
    protected function retryDelay()
    {
        return function ($numberOfRetries) {
            return 1000 * $numberOfRetries;
        };
    }

    /**
     * 获取扫码的授权信息
     * @throws NotFoundExceptionInterface
     * @throws ApiException
     * @throws ContainerExceptionInterface
     * @throws GuzzleException
     * @throws InvalidArgumentException
     */
    public function getQueryAuthInfo(string $authCode): StreamInterface|array
    {
        $componentAccessToken = $this->getComponentAccessTokenFromCache();

        $params = [
            'component_appid' => $this->appKey,
            'authorization_code' => $authCode
        ];
        Log::debug('getQueryAuthInfo params', [$params]);
        $httpClient = $this->getHttpClient();
        $response = $httpClient->post($this->authUrl . '/cgi-bin/component/api_query_auth?component_access_token=' . $componentAccessToken, [
            'json' => $params,
            'headers' => [
                'Content-type' => 'application/json',
                "Accept" => "application/json"
            ],
        ]);

        $authInfo =$this->handleResponse($response->getBody());
        return $authInfo['authorization_info'] ?? [];
    }

    public function executeRequest($request, $accessToken)
    {
        // TODO: Implement executeRequest() method.
    }
}
