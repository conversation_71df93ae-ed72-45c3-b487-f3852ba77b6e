<?php

namespace App\Components\Facade4Api\Items\Impl;

use App\Components\Facade4Api\Client\JdClient;
use App\Components\Facade4Api\Constants\PlatformConst;
use App\Components\Facade4Api\Items\AbstractItemsFacade;
use App\Components\Facade4Api\Items\FaItem;
use App\Components\Facade4Api\Items\FaItemSku;
use App\Components\Facade4Api\Page;
use App\Utils\Log;
use Hyperf\Collection\Collection;
use SkuReadSearchSkuListRequest;
use WareReadFindWareByIdRequest;

/**
 * JD的Goods 服务
 */
class JdItemImpl extends AbstractItemsFacade
{

    protected string $platform = PlatformConst::JD;


    /**
     * @inheritDoc
     */
    public function formatToFaItems(array $items, string $accessToken = null): Collection
    {
        $result = new Collection();
        foreach ($items as $index => $item) {
            $faItem = $this->formatToFaItem($item,$accessToken);
            $result->push($faItem);
        }

        return $result;
    }

    /**
     * 商品构建
     * @param array $item
     * @param string|null $accessToken
     * @return FaItem
     */
    public function formatToFaItem(array $item, string $accessToken = null): FaItem
    {
        $faItemSkus = new Collection();
        $skuList = $this->getSkuList($item['wareId'],$accessToken);

        foreach ($skuList as $index => $sku) {
            $faItemSku=new FaItemSku();
            $faItemSku->platform=$this->platform;
            $faItemSku->skuId = $sku['skuId'];
            $faItemSku->skuName= $sku['multiCateProps'][0]['attrValueAlias'][0] ?? 0;
            $faItemSku->outId= $sku['outerId'] ?? '';
            $faItemSku->outItemId=$goods['outerId'] ?? '';
            $faItemSku->price=$sku['jdPrice'] ?? null;
            $faItemSku->picUrl=isset($sku['logo'])?'https://img10.360buyimg.com/n0/' . $sku['logo'] : '';
            $faItemSku->status=1;
            $faItemSkus->push($faItemSku);
        }
        $faItem=new FaItem();
        $faItem->platform=$this->platform;
        $faItem->numIid= $item['wareId'] ?? '';
        $faItem->outerId= $item['outerId'] ?? '';
        $faItem->title=$item['title'];
        $faItem->picUrl=isset($item['logo']) ? 'https://img10.360buyimg.com/n0/' . $item['logo'] : '';
        $faItem->status = FaItem::ON_SALE_YES;
        $faItem->ptCreatedAt= $item['created'];
        $faItem->ptUpdatedAt=$item['modified'];
        $faItem->itemSkus=$faItemSkus;
        return $faItem;
    }

    public function getSkuList($goodId,string $accessToken)
    {
        $client = JdClient::newSdkClient($accessToken);
        $req = new SkuReadSearchSkuListRequest();

        $req->setWareId($goodId);
        $req->setField('wareId,skuId,status,jdPrice,outerId,logo,skuName,wareTitle,modified,created,multiCateProps');


        $resp = $client->execute($req, $accessToken);
        JdClient::handleErrorCode($resp);


        return $resp['jingdong_sku_read_searchSkuList_responce']['page']['data'] ?? [];
    }


    protected function getJdClient(string $accessToken): JdClient
    {
        return JdClient::newInstance($accessToken);
    }

    public function sendGetItems(string $accessToken, int $pageSize, int $currentPage,?int $onSale=1): Page
    {
        $page = new Page();
        $page->currentPage = $currentPage;
        $page->pageSize = $pageSize;
        $client = JdClient::newSdkClient($accessToken);
        $req = new \WareReadSearchWare4ValidRequest();
        $req->setPageNo($currentPage);
        $req->setPageSize($pageSize);
        $req->setWareStatusValue('8');
        $req->setField('wareId,title,categoryId,brandId,templateId,wareStatus,outerId,itemNum,modified,created,weight,width,height,length,images,logo,marketPrice,jdPrice,multiCateProps');

        $resp = $client->execute($req, $accessToken);
        $resp = json_decode(json_encode($resp), true);
//        JdClient::handleResponse($resp);
        if (isset($resp['jingdong_ware_read_searchWare4Valid_responce']['page']['totalItem'])) {
            $this->goodsTotalCount = $resp['jingdong_ware_read_searchWare4Valid_responce']['page']['totalItem'];
        } else {
            $this->goodsTotalCount = 0;
        }

        $arr = [];
        if (isset($resp['jingdong_ware_read_searchWare4Valid_responce']['page'])) {
            if ($resp['jingdong_ware_read_searchWare4Valid_responce']['page']['pageNo'] <
                ($resp['jingdong_ware_read_searchWare4Valid_responce']['page']['totalItem'] / $resp['jingdong_ware_read_searchWare4Valid_responce']['page']['pageSize'])) {
                $page->hasNext=true;
            }

            $arr = $resp['jingdong_ware_read_searchWare4Valid_responce']['page']['data'];
        }

        $page->data = $this->formatToFaItems($arr,$accessToken)->toArray();
        return $page;
    }

    public function sendGetItemsByNumIids(string $accessToken, array $numIids):Collection
    {
        $client = $this->getJdClient($accessToken);

        $result = $data = [];
        foreach ($numIids as $index => $wareId) {
            $req = new WareReadFindWareByIdRequest();
            $req->setWareId($wareId);
            $req->setField('wareId,title,categoryId,brandId,templateId,wareStatus,outerId,itemNum,modified,created,weight,width,height,length,images,logo,marketPrice,jdPrice,multiCateProps');
            $data[$index] =$client->convertApiRequest2PsrRequest($req);
        }
        $curlResults = $client->poolCurl($data,"post_form");
        foreach ($curlResults as $index => $response) {
            try {
                $resp = $client->handleResponse($response->body);
                if (isset($resp['jingdong_ware_read_findWareById_responce']['ware'])) {
                    $result[$index] = $resp['jingdong_ware_read_findWareById_responce']['ware'];
                }
            } catch (\Exception $e) {
                Log::info('京东同步商品请求出错 sync goods error:' . $e->getMessage());
            }
        }

        return $this->formatToFaItems($result,$accessToken);
    }
}
