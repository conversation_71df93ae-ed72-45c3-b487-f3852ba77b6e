<?php

namespace App\Components\Facade4Api\Controller;


use App\Components\Facade4Api\Client\WxClient;
use App\Components\Facade4Api\Exception\ApiException;
use App\Components\Facade4Api\Exception\ErrorConstants;
use App\Components\Facade4Api\Wx\WxBizMsgCrypt;
use App\Components\Facade4Api\Wx\WxNotifyType;
use App\Utils\Log;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\PostMapping;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\HttpServer\Contract\ResponseInterface;
use Hyperf\Redis\Redis;
use Hyperf\Redis\RedisFactory;

/**
 * 微信通知的
 */
#[Controller(prefix: "/wx")]
class WxNotifyController
{

    /**
     *
     * @param RequestInterface $request
     * 消息事件接收处理
     */
    #[PostMapping(path: "ticket/notify")]
    public function getTicket(RequestInterface $request, ResponseInterface $response)
    {
        $redis = WxClient::getRedis();
//		Log::info('all query params', [$request->all()]);
        $xmlMsg = $request->getBody()->getContents();
//        var_dump($xmlMsg);
        Log::info('xml_msg', [$xmlMsg]);

        $timeStamp = !empty($request->input('timestamp')) ? $request->input('timestamp') : '';
        $nonce = !empty($request->input('nonce')) ? $request->input('nonce') : '';
        $encryptType = !empty($request->input('encrypt_type')) ? $request->input('encrypt_type') : '';
        $msgSign = !empty($request->input('msg_signature')) ? $request->input('msg_signature') : '';

        $xmlData = $this->decryptMsg($timeStamp, $nonce, $encryptType, $msgSign, $xmlMsg);
        $xml = new \DOMDocument();
        $xml->loadXML($xmlData);
        $array_e = $xml->getElementsByTagName('InfoType');
        $infoType = $array_e->item(0)->nodeValue;

        //消息处理
        switch ($infoType) {
            case WxNotifyType::COMPONENT_VERIFY_TICKET :
                $array_e = $xml->getElementsByTagName('ComponentVerifyTicket');
                $componentVerifyTicket = $array_e->item(0)->nodeValue;
                Log::info('component_verify_ticket', [$componentVerifyTicket]);
                if ($componentVerifyTicket) {

                    $result = $redis->setex(WxClient::COMPONENT_VERIFY_TICKET_REDIS_KEY, 30 * 60, $componentVerifyTicket);
                    Log::info("设置component_verify_ticket" . ($result ? '成功' : '失败'));
                }
                break;

            case WxNotifyType::AUTHORIZED:
                $array_e = $xml->getElementsByTagName('AuthorizationCode');
                $authorizationCode = $array_e->item(0)->nodeValue;
                Log::info('authorized', [$authorizationCode]);
                break;

            case WxNotifyType::UPDATE_AUTHORIZED:
                $array_e = $xml->getElementsByTagName('AuthorizationCode');
                $authorizationCode = $array_e->item(0)->nodeValue;
                Log::info('updateauthorized', [$authorizationCode]);
                break;

            case WxNotifyType::UN_AUTHORIZED:
                // 这块代码有问题，暂时不用
//				$array_e           = $xml->getElementsByTagName('AuthorizationCode');
//				$authorizationCode = $array_e->item(0)->nodeValue;
//				Log::info('unauthorized', [$authorizationCode]);
                break;
            default:
                echo 'failed';
                break;
        }
        return $response->raw("success");
//        echo "success";
    }

    /**
     * 消息解密
     * @param $timeStamp
     * @param $nonce
     * @param $encrypt_type
     * @param $msg_sign
     * @param $encryptMsg
     * @return string
     */
    public function decryptMsg($timeStamp, $nonce, $encrypt_type, $msg_sign, $encryptMsg): string
    {
        $encodingAesKey = WxClient::getConfig('msg_key');
        $token = WxClient::getConfig('msg_token');
        $appId = WxClient::getConfig('client_id');
        Log::info("解密参数", [$encodingAesKey, $token, $appId]);
        $pc = new WxBizMsgCrypt($token, $encodingAesKey, $appId);
        $xml_tree = new \DOMDocument();
        $xml_tree->loadXML($encryptMsg);
        $array_e = $xml_tree->getElementsByTagName('Encrypt');
        $encrypt = $array_e->item(0)->nodeValue;

        $format = "<xml><ToUserName><![CDATA[toUser]]></ToUserName><Encrypt><![CDATA[%s]]></Encrypt></xml>";
        $from_xml = sprintf($format, $encrypt);

        $msg = '';
        $errCode = $pc->decryptMsg($msg_sign, $timeStamp, $nonce, $from_xml, $msg);
        if ($errCode != 0) {
            Log::info('解密错误', [$errCode]);
            ApiException::throwException(ErrorConstants::WX_DECRYPT_FAIL[1], ErrorConstants::WX_DECRYPT_FAIL[0]);
        }
        return $msg;
    }

}
