<?php

namespace App\Components\Facade4Api\Util;

use InvalidArgumentException;

/**
 * 字符串
 */
class Str
{

    /**
     * 是否是HTTP URL
     * @param string $input
     * @return bool
     */
    public static function isHttpUrl(string $input):bool
    {
        if(empty($input)){
            return false;
        }
        return self::startWith(strtolower($input), 'http');
    }

    public static function  concatUrlPath($prefix,$end):string{
        if(empty($prefix)){
            $prefix="";
        }
        if(empty($end)){
            $end="";
        }
        return  $prefix."/".$end;

    }

    public static function startWith(string $input,string $prefix):bool{
        if (strlen($prefix) === 0) {
            throw new InvalidArgumentException('入参不能是空字符串');
        }

        return str_starts_with($input, $prefix);
    }


}