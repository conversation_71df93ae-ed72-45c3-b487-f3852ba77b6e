<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/3/17
 * Time: 15:54
 */

namespace App\Components\Facade4Api\Constants;


class PlatformConst
{
    //店铺
    /**
     * @var string 平台淘宝
     */
    const TAOBAO = 'tb';
    const PDD = 'pdd';
    const KS = 'ks';
    const JD = 'jd';
    const WX = 'wx';
    const DY = 'dy';
    const ALBB = 'albb'; //阿里巴巴
    const CN = 'cn'; //菜鸟站外电子面单
    const ALC2M = 'alc2m'; //阿里巴巴C2M
    const XHS = 'xhs'; //小红书
    const DEWU = 'dewu'; //得物

    const PLATFORM_MAP = [
        self::TAOBAO => [
            'name' => '淘宝',
        ],
        self::PDD => [
            'name' => '拼多多',
        ],
        self::KS => [
            'name' => '快手',
        ],
        self::JD => [
            'name' => '京东',
        ],
        self::WX => [
            'name' => '微信',
        ],
        self::DY => [
            'name' => '抖音',
        ],
        self::ALBB => [
            'name' => '阿里巴巴',
        ],
        self::XHS => [
            'name' => '小红书',
        ],
        self::DEWU => [
            'name' => '得物',
        ],
        'sys' => [
            'name' => '自定义',
        ],
    ];
    const NAME_2_PLATFORM_MAP = [
        '拼多多' => ['code' => 'pdd', 'type' => 1],
        '淘宝' => ['code' => 'tb', 'type' => 2],
        '抖音' => ['code' => 'dy', 'type' => 3],
        '快手' => ['code' => 'ks', 'type' => 5],
        '京东' => ['code' => 'jd', 'type' => 4],
        '微信' => ['code' => 'wx', 'type' => 6],
        '阿里巴巴' => ['code' => 'albb', 'type' => 7],
    ];


//    //平台类型
//    const PLATFORM_TYPE_TAOBAO = 1;
//    const PLATFORM_TYPE_PDD = 2;
//    const PLATFORM_TYPE_KS = 3;
//    const PLATFORM_TYPE_JD = 4;
//    const PLATFORM_TYPE_DY = 5;
//    const PLATFORM_TYPE_WX = 6;


//    //电子面单
//    const PDD_WB     = 'pddwb';   //pdd站外面单
//    const TWC        = 'twc';    //第三方淘宝面单
//    const NEW_TWC    = 'newtwc'; //自己的淘宝服务
//    const LINK       = 'link';   //菜鸟Link
//    const TB         = 'taobao'; //淘宝


}
