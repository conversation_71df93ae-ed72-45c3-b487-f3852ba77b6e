<?php

namespace App\Components\Facade4Api\AfterSale\Impl;

use App\Components\Facade4Api\AfterSale\AbstractAfterSaleFacade;
use App\Components\Facade4Api\AfterSale\FaAfterSale;
use App\Components\Facade4Api\AfterSale\Request\AfterSalePageRequest;
use App\Components\Facade4Api\Client\TbClient;
use App\Components\Facade4Api\Page;
use App\Constants\AfterSaleConstant;
use App\Convert\OrderConvert;
use App\Request\AfterSaleGood\AfterSaleGoodUpdateRequest;
use App\Utils\DateTimeUtil;
use TopClient\request\RefundsReceiveGetRequest;

class TbAfterSaleImpl extends AbstractAfterSaleFacade
{

    public function getAfterSaleList(string $accessToken, AfterSalePageRequest $request): Page
    {
        $tbClient = TbClient::newInstance($accessToken);
        $req = new RefundsReceiveGetRequest();
        $req->setFields("refund_id,tid,title,buyer_nick,seller_nick,total_fee,status,created,refund_fee,oid,good_status,company_name,sid,payment,reason,desc,has_good_return,modified,order_status,refund_phase,sku,num");
        // $req->setStatus("WAIT_SELLER_AGREE");
        $req->setType("fixed");
        $req->setStartModified(DateTimeUtil::format($request->start));
        $req->setEndModified(DateTimeUtil::format($request->end));
        $req->setPageNo(($request->page ?? 0) + 1 . '');
        $req->setPageSize($request->pageSize . '');
        $req->setUseHasNext($request->page === 0 ? "false" : "true");
        $response = $tbClient->executeRequest($req, $accessToken);
        $data = $this->formatToAfterSaleList($response['refunds']['refund']);
        $hasNext = $response['has_next'] ?? false;
        $total = $response['total_results'] ?? 0;
        $res = Page::newInstance($request->page ?? 0, $request->pageSize ?? 100, $data, $hasNext, $total);
        return $res;
    }

    function formatToAfterSaleList($items): array
    {
        $res = [];
        foreach ($items as $item) {
            $res[] = $this->formatToAfterSale($item);
        }
        return $res;
    }

    function formatToAfterSale(array  $afterSale,?array $orderInfo): FaAfterSale
    {
        $re = new FaAfterSale();

        $re->sysType = $this->convertSysType($afterSale);
        $re->sysStatusSub = $this->convertSysStatusSub($afterSale);
        $re->sysStatus = $this->convertSysStatus($afterSale, $re->sysStatusSub);

        $re->afterSaleNo = $afterSale['refund_id'];
        $re->afterSaleStatus = $re->sysStatusSub;
        $re->afterSaleType = $re->sysType;
        $re->refundStatus = $afterSale['refund_fee'] > 0 ? 1 : 0;
        $re->afterSaleReason = $afterSale['reason'];
        $re->afterSaleDesc = empty($afterSale['desc']) ? '' : $afterSale['desc'];
        $re->refundAmount = $afterSale['refund_fee'];
        $re->tid = $afterSale['tid'];
        $re->orderStatus = OrderConvert::convertTbOrderStatus($afterSale['order_status']);
        $re->returnWaybillCode = $afterSale['sid'] ?? '';
        $re->returnWpName = $afterSale['company_name'] ?? '';
        $re->createdTime = $afterSale['created'];
        $re->updatedTime = $afterSale['modified'];

        $g = new AfterSaleGoodUpdateRequest();
        $g->tid = $afterSale['tid'];
        $g->oid = $afterSale['oid'];
        $g->orderStatus = $re->orderStatus;
        $g->goodName = $afterSale['title'];
        $g->goodNumber = $afterSale['num'];
        $skuArr = explode('|', $afterSale['sku']);
        $g->skuId = (int)$skuArr[0];
        $g->skuName = $skuArr[1];
        $g->outerGoodId = $afterSale['outer_id'] ?? '';

        $re->afterSaleGoods = [$g];
        $re->otherInfo = json_encode([
            'status' => $afterSale['status'],
            'order_status' => $afterSale['order_status'],
            'operation_contraint' => $afterSale['operation_contraint'] ?? null,
        ]);
        return $re;
    }

    private function convertSysType($afterSale): int
    {
        $returnGood = $afterSale['has_good_return'];
        $orderStatus = $afterSale['order_status'];
        if ($returnGood === 'false' && $orderStatus === 'WAIT_SELLER_SEND_GOODS') {
            return AfterSaleConstant::SYS_TYPE_NOT_SEND_REFUND;
        }
        if ($returnGood === 'false' && ($orderStatus === 'WAIT_BUYER_CONFIRM_GOODS' || $orderStatus === 'TRADE_BUYER_SIGNED')) {
            return AfterSaleConstant::SYS_TYPE_SEND_REFUND;
        }
        if ($returnGood === 'true' && ($orderStatus === 'WAIT_BUYER_CONFIRM_GOODS' || $orderStatus === 'TRADE_BUYER_SIGNED')) {
            return AfterSaleConstant::SYS_TYPE_RETURN_GOOD;
        }
        return AfterSaleConstant::OTHER;
    }

    private function convertSysStatusSub($afterSale): int
    {
        $status = $afterSale['status'];
        if ('SELLER_REFUSE_BUYER' === $status) {
            return AfterSaleConstant::REJECTED_REFUND;
        }
        if ('WAIT_BUYER_RETURN_GOODS' === $status) {
            return AfterSaleConstant::WAIT_BUYER_RETURN_GOOD;
        }
        if ('WAIT_SELLER_CONFIRM_GOODS' === $status) {
            return AfterSaleConstant::WAIT_SHOP_RECEIVE_GOOD;
        }
        if ('CLOSED' === $status) {
            return AfterSaleConstant::REFUND_CLOSE;
        }
        if ('SUCCESS' === $status) {
            return AfterSaleConstant::REFUND_SUCCESS;
        }
        return -1;
    }

    private function convertSysStatus($afterSale, ?int $sysStatusSub): int
    {
        if (in_array($sysStatusSub, AfterSaleConstant::DEADING_ARRAY)) {
            return AfterSaleConstant::DEALING;
        }
        if (in_array($sysStatusSub, AfterSaleConstant::DEALED_ARRAY)) {
            return AfterSaleConstant::DEALED;
        }
        if (in_array($sysStatusSub, AfterSaleConstant::WAIT_DEAL_ARRAY)) {
            return AfterSaleConstant::WAIT_DEAL;
        }
        return AfterSaleConstant::OTHER;
    }

    public function listRefundAddress(string $accessToken): array
    {
        // TODO: Implement listRefundAddress() method.
    }
}