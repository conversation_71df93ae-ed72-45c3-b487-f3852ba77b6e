<?php

namespace App\Components\Facade4Api\AfterSale;

use App\Components\Facade4Api\AfterSale\Request\AfterSalePageRequest;
use App\Components\Facade4Api\Page;

abstract class AbstractAfterSaleFacade
{
    /**
     * 拉取平台退款审核单列表(同步脚本使用)
     * @param string $accessToken
     * @param AfterSalePageRequest $request
     * @return Page
     */
    abstract public function getAfterSaleList(string $accessToken, AfterSalePageRequest $request): Page;

    /**
     * 获取单笔售后详情
     * 根据订单模型的不同，售后模型也会不同
     * PDD一个订单只有一个SKU，所以售后单也只有一个
     * 其他的购物车订单可能有多个SKU，所以售后单也可能有多个
     * @param string $accessToken
     * @param string $orderSn
     * @param string $afterSaleId
     * @return FaAfterSale
     */
    public function getAfterSale(string $accessToken,string $orderSn, string $afterSaleId): FaAfterSale{
        return new FaAfterSale();
    }

    /**
     * 根据订单号获取售后详情，一个订单可能有多个售后单
     * @param string $accessToken
     * @param string $tid
     * @return  FaAfterSale[]
     */
    public function getAfterSaleByTid(string $accessToken,string $tid): array{
        return [];
    }
    /**
     * 批量转换成AfterSale模型
     * @param array $items
     * @return mixed
     */
    abstract function formatToAfterSaleList(array $items): array;

    /**
     * 转换单个售后请求，一个售后单可以可能关联多个sku
     * 有些售后接口返回的数据不含订单信息，需要单独调用订单接口获取
     * @param array $afterSale
     * @param array|null $orderInfo - 订单信息
     * @return mixed
     */
    abstract function formatToAfterSale(array $afterSale,?array $orderInfo): FaAfterSale;

    /**
     * 售后操作-同意
     * @param string $accessToken
     * @param FaAfterSaleOperation[] $operations
     * @return array -1 未操作， 0 成功
     */
    public function agreeAfterSale(string $accessToken, array $operations): array
    {
        return [];
    }

    /**
     * 获取售后退货地址
     * @param string $accessToken
     * @return FaRefundAddress[]
     */
    abstract  public function listRefundAddress(string $accessToken): array;
    public function commonApi(string $access_token, string $uri, ?array $params)
    {
        return [];
    }

    /**
     * 返回同步脚本每次拉取的数量
     * @return int
     */
    public function getSyncPageSize(): int
    {
        return 100;
    }
}