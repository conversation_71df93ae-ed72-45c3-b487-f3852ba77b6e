<?php

namespace App\Repository\Shop;

use App\Model\Shop\Shop;
use App\Repository\IRepository;
use App\Model\Shop\Shop as Model;
use App\Utils\Log;
use Hyperf\Carbon\Carbon;
use Hyperf\Database\Model\Builder;


/**
* ShopRepository
* @extends IRepository<Model>
*/
class ShopRepository extends IRepository
{
    public function __construct(
        protected readonly Model $model
    ) {}

    public function handleSearch(Builder $query, array $params): Builder
    {
                                    
        if (isset($params['id'])) {
            $query->where('id', $params['id']);
        }
        if (isset($params['ids'])) {
            $query->whereIn('id', $params['ids']);
        }
        if (isset($params['shop_name'])) {
            $query->where('shop_name', $params['shop_name']);
        }
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                
        return $query;
    }

    public function getQueryByAuthAvailable()
    {
        return $this->getQuery()
            ->where('auth_status', Shop::AUTH_STATUS_SUCCESS)
            ->whereNotNull('access_token')
            ->where('access_token', '<>', '')
//            ->where('app_id', '<>', 'old')
            ->where('expire_at', '>', Carbon::now()->toDateTimeString());
    }

    /**
     * 更新最后一次授权时间
     * @param $id
     * @param $time
     * @param bool $ifNewest 是否判断是最新的授权时间，则更新最新授权时间
     * <AUTHOR>
     */
    public function updateLastSync($id, $time, bool $ifNewest = true)
    {
        $saveData = [
            'last_sync_at' => $time,
//            'last_sync_page' => $page,
        ];
        $query = Shop::query(true)->where('id', $id);
        if ($ifNewest) {
            $query->where(function ($query) use ($time) {
                $query->where('last_sync_at', '<', $time);
                $query->orWhereNull('last_sync_at');
            });
        }
        $res = $query->update($saveData);
        Log::info('updateLastSync:' . $id, ['id' => $id, 'time' => $time, 'ifNewest' => $ifNewest, 'res' => $res]);
    }
}
