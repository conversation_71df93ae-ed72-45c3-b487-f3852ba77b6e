<?php

namespace App\Repository\Product;

use App\Repository\IRepository;
use App\Model\Product\PickTaskCode as Model;
use Hyperf\Database\Model\Builder;


/**
* PickTaskCodeRepository
* @extends IRepository<Model>
*/
class PickTaskCodeRepository extends IRepository
{
    const ASSIGN_STATUS_ASSIGNED = 1; // 已分配
    const ASSIGN_STATUS_UNASSIGNED = 0; // 未分配

    public function __construct(
        protected readonly Model $model
    ) {}

    public function handleSearch(Builder $query, array $params): Builder
    {
                                                                                                                                                                                                                
        return $query;
    }
}
