<?php

namespace App\Service\User;

use App\Exception\BusinessException;
use App\Http\Admin\Request\User\UserRelationCreateRequest;
use App\Http\Admin\Request\User\UserRelationListRequest;
use App\Http\Common\ResultCode;
use App\Model\Permission\User;
use App\Model\User\UserRelation;
use App\Repository\Permission\UserRepository;
use App\Service\IService;
use App\Repository\User\UserRelationRepository as Repository;



class UserRelationService extends IService
{
    public function __construct(
        protected readonly Repository $repository,
        protected readonly UserRepository $userRepository
    ) {}

    public function getRepository(): Repository
    {
        return $this->repository;
    }

    public function list(array $getRequestData)
    {
        $list = $this->getList($getRequestData);
        $list->whith(['relatedUser']);
        return $list;
    }

    public function createBind(UserRelationCreateRequest $request, \App\Http\CurrentUser $currentUser)
    {
        $bindUser = $this->userRepository->findByPhone($request->phone);
        if (empty($bindUser)){
            throw new BusinessException(ResultCode::BAD_REQUEST, '用户不存在');
        }
        // 判断是否已经存在绑定
        $exists = $this->repository->findByUserAndRelatedUser($currentUser->id(), $bindUser->id);
        if ($exists) {
            throw new BusinessException(ResultCode::BAD_REQUEST, '已经存在绑定关系');
        }
        $this->create([
            'user_id' => $currentUser->id(),
            'related_user_id' => $bindUser->id,
            'relation_type' => $request->relation_type,
            'status' => UserRelation::STATUS_NORMAL,
            'name' => $request->name,
        ]);
    }

}
