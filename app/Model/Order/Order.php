<?php

namespace App\Model\Order;

use Hyperf\DbConnection\Model\Model as MineModel;


/**
 * Class orders
 * @property integer $id  
 * @property integer $shop_id  店铺id
 * @property string $tid  订单号
 * @property string $free_order_no  自由打印订单编号
 * @property integer $type  平台类型
 * @property integer $order_type  订单类型 1平台订单 2自由打印
 * @property integer $order_biz_type  订单业务类型
 * @property integer $free_order_source  自由打印订单来源
 * @property string $waybill_code  面单号
 * @property string $union_wp_code  物流公司编码
 * @property string $buyer_id  买家id
 * @property string $buyer_nick  买家昵称
 * @property string $seller_nick  卖家昵称
 * @property string $shop_title  店铺标题
 * @property float $payment  支付金额
 * @property float $total_fee  订单金额
 * @property float $discount_fee  优惠金额
 * @property float $post_fee  邮费
 * @property float $collection_fee  代收货款
 * @property string $custom_group  自定义分组
 * @property integer $order_status  订单状态
 * @property integer $after_sales_status  售后状态
 * @property integer $print_status  打印状态
 * @property integer $print_num  打印次数
 * @property string $printed_at  打印时间
 * @property string $print_shipping_at  打印发货时间
 * @property string $receiver_province  收货人省
 * @property string $receiver_city  收货人市
 * @property string $receiver_district  收货人区
 * @property string $receiver_town  收货人镇
 * @property string $receiver_name  收货人姓名
 * @property string $receiver_phone  收货人手机
 * @property string $receiver_tel  收货人电话
 * @property integer $receiver_zip  收货人邮编
 * @property string $receiver_address  收货人地址
 * @property string $receiver_md5  收货人信息 md5
 * @property integer $sender_address_id  发件人地址ID,这个关联到发货地址管理里面的ID
 * @property string $sender_name  发件人
 * @property string $sender_province  发件人省
 * @property string $sender_city  发件人市
 * @property string $sender_district  发件人区
 * @property string $sender_town  发件人街道
 * @property string $sender_zip  发件人邮编
 * @property string $sender_address  发件人详细地址
 * @property string $sender_phone  发件人手机
 * @property string $sender_tel  发件人电话
 * @property integer $district_code  区域编码
 * @property string $address_md5  地址md5
 * @property integer $address_flag  地址标识
 * @property integer $address_village_flag  地址村标识
 * @property string $seller_flag  卖家旗帜
 * @property string $seller_memo  卖家备注
 * @property string $platform_memo  平台备注
 * @property string $buyer_message  买家留言
 * @property integer $has_buyer_message  是否有买家留言
 * @property integer $is_comment  是否评价
 * @property integer $goods_total_num  商品总数量
 * @property integer $sku_num  sku数量
 * @property integer $is_pre_sale  是否预售
 * @property string $merge_flag  合并标识
 * @property string $smart_logistics  智能物流
 * @property string $store_id  门店id
 * @property string $custom_print_content  自定义打印内容
 * @property string $platform_logistics  平台指定物流
 * @property string $software_logistics  软件指定物流
 * @property string $urge_delivery_at  催发货时间
 * @property string $recycled_at  回收时间
 * @property string $promise_ship_at  承诺发货时间
 * @property string $send_at  发货时间
 * @property string $confirm_at  确认收货时间
 * @property string $locked_at  锁定时间
 * @property string $pay_at  付款时间
 * @property string $group_at  成团时间
 * @property string $modify_address_at  修改收货地址时间
 * @property string $order_created_at  订单创建时间
 * @property string $order_updated_at  订单更新时间
 * @property string $created_at  
 * @property string $updated_at  
 * @property string $deleted_at  
 */
class Order extends MineModel
{
    protected ?string $table = 'orders';

    protected array $fillable = ['id','shop_id','tid','free_order_no','type','order_type','order_biz_type','free_order_source','waybill_code','union_wp_code','buyer_id','buyer_nick','seller_nick','shop_title','payment','total_fee','discount_fee','post_fee','collection_fee','custom_group','order_status','after_sales_status','print_status','print_num','printed_at','print_shipping_at','receiver_province','receiver_city','receiver_district','receiver_town','receiver_name','receiver_phone','receiver_tel','receiver_zip','receiver_address','receiver_md5','sender_address_id','sender_name','sender_province','sender_city','sender_district','sender_town','sender_zip','sender_address','sender_phone','sender_tel','district_code','address_md5','address_flag','address_village_flag','seller_flag','seller_memo','platform_memo','buyer_message','has_buyer_message','is_comment','goods_total_num','sku_num','is_pre_sale','merge_flag','smart_logistics','store_id','custom_print_content','platform_logistics','software_logistics','urge_delivery_at','recycled_at','promise_ship_at','send_at','confirm_at','locked_at','pay_at','group_at','modify_address_at','order_created_at','order_updated_at','created_at','updated_at','deleted_at',];

    protected array $casts = ['id' => 'integer','shop_id' => 'integer','tid' => 'string','free_order_no' => 'string','type' => 'integer','order_type' => 'integer','order_biz_type' => 'integer','free_order_source' => 'integer','waybill_code' => 'string','union_wp_code' => 'string','buyer_id' => 'string','buyer_nick' => 'string','seller_nick' => 'string','shop_title' => 'string','payment' => 'float','total_fee' => 'float','discount_fee' => 'float','post_fee' => 'float','collection_fee' => 'float','custom_group' => 'string','order_status' => 'integer','after_sales_status' => 'integer','print_status' => 'integer','print_num' => 'integer','printed_at' => 'string','print_shipping_at' => 'string','receiver_province' => 'string','receiver_city' => 'string','receiver_district' => 'string','receiver_town' => 'string','receiver_name' => 'string','receiver_phone' => 'string','receiver_tel' => 'string','receiver_zip' => 'integer','receiver_address' => 'string','receiver_md5' => 'string','sender_address_id' => 'integer','sender_name' => 'string','sender_province' => 'string','sender_city' => 'string','sender_district' => 'string','sender_town' => 'string','sender_zip' => 'string','sender_address' => 'string','sender_phone' => 'string','sender_tel' => 'string','district_code' => 'integer','address_md5' => 'string','address_flag' => 'integer','address_village_flag' => 'integer','seller_flag' => 'string','seller_memo' => 'string','platform_memo' => 'string','buyer_message' => 'string','has_buyer_message' => 'integer','is_comment' => 'integer','goods_total_num' => 'integer','sku_num' => 'integer','is_pre_sale' => 'integer','merge_flag' => 'string','smart_logistics' => 'string','store_id' => 'string','custom_print_content' => 'string','platform_logistics' => 'string','software_logistics' => 'string','urge_delivery_at' => 'string','recycled_at' => 'string','promise_ship_at' => 'string','send_at' => 'string','confirm_at' => 'string','locked_at' => 'string','pay_at' => 'string','group_at' => 'string','modify_address_at' => 'string','order_created_at' => 'string','order_updated_at' => 'string','created_at' => 'string','updated_at' => 'string','deleted_at' => 'string',];

    public function items(): \Hyperf\Database\Model\Relations\HasMany
    {
        return $this->hasMany(OrderItem::class, 'order_id', 'id');
    }
    public function orderCipherInfo(): \Hyperf\Database\Model\Relations\HasOne
    {
        return $this->hasOne(OrderCipherInfo::class, 'order_id', 'id');
    }
}