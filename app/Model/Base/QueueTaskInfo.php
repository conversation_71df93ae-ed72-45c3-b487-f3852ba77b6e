<?php

namespace App\Model\Base;

use Hyperf\DbConnection\Model\Model as MineModel;


/**
* Class queue_task_info
* @property string $id 
* @property string $shop_id 
* @property string $batch_no 
* @property string $task_status 
* @property string $task_desc 
* @property string $task_param 
* @property string $total 
* @property string $success 
* @property string $failed 
* @property string $created_by 
* @property string $updated_by 
* @property string $created_at 
* @property string $updated_at 
*/
class QueueTaskInfo extends MineModel
{
    protected ?string $table = 'queue_task_info';

    protected array $fillable = ['id','shop_id','batch_no','task_status','task_desc','task_param','total','success','failed','created_by','updated_by','created_at','updated_at',];

    protected array $casts = ['id' => 'string','shop_id' => 'string','batch_no' => 'string','task_status' => 'string','task_desc' => 'string','task_param' => 'string','total' => 'string','success' => 'string','failed' => 'string','created_by' => 'string','updated_by' => 'string','created_at' => 'string','updated_at' => 'string',];
}