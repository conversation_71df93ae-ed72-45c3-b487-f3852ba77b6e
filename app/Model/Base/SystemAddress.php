<?php

namespace App\Model\Base;

use Hyperf\DbConnection\Model\Model as MineModel;


/**
* Class system_address
* @property string $id 
* @property string $type 
* @property string $code 
* @property string $parent_code 
* @property string $name 
* @property string $level 
* @property string $system_code 
* @property string $created_at 
* @property string $updated_at 
*/
class SystemAddress extends MineModel
{
    protected ?string $table = 'system_address';

    protected array $fillable = ['id','type','code','parent_code','name','level','system_code','created_at','updated_at',];

    protected array $casts = ['id' => 'string','type' => 'string','code' => 'int','parent_code' => 'int','name' => 'string','level' => 'int','system_code' => 'int','created_at' => 'string','updated_at' => 'string',];

    const TYPE_SYS = 1;
    const TYPE_PT = 2;

    /**
     * 其他区
     */
    const OTHER_DISTRICT_NAME = '其他区';
    const OTHER_DISTRICT_CODE = 9999;
}