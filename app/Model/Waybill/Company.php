<?php

namespace App\Model\Waybill;

use Hyperf\DbConnection\Model\Model as MineModel;


/**
* Class companies
* @property string $id 
* @property string $user_id 
* @property string $shop_id 
* @property string $auth_source 
* @property string $owner_id 
* @property string $owner_name 
* @property string $branch_name 
* @property string $branch_code 
* @property string $wp_code 
* @property string $wp_name 
* @property string $wp_type 
* @property string $status 
* @property string $source 
* @property string $source_userid 
* @property string $source_shopid 
* @property string $source_status 
* @property string $quantity 
* @property string $cancel_quantity 
* @property string $recycled_quantity 
* @property string $allocated_quantity 
* @property string $templates 
* @property string $province 
* @property string $city 
* @property string $district 
* @property string $street 
* @property string $detail 
* @property string $settlement_code 
* @property string $platform_account_id 
* @property string $platform_shop_id 
* @property string $extended_info 
* @property string $created_by 
* @property string $updated_by 
* @property string $created_at 
* @property string $updated_at 
* @property string $deleted_at 
*/
class Company extends MineModel
{
    protected ?string $table = 'companies';

    protected array $fillable = ['id','user_id','shop_id','auth_source','owner_id','owner_name','branch_name','branch_code','wp_code','wp_name','wp_type','status','source','source_userid','source_shopid','source_status','quantity','cancel_quantity','recycled_quantity','allocated_quantity','templates','province','city','district','street','detail','settlement_code','platform_account_id','platform_shop_id','extended_info','created_by','updated_by','created_at','updated_at','deleted_at',];

    protected array $casts = ['id' => 'string','user_id' => 'string','shop_id' => 'string','auth_source' => 'string','owner_id' => 'string','owner_name' => 'string','branch_name' => 'string','branch_code' => 'string','wp_code' => 'string','wp_name' => 'string','wp_type' => 'string','status' => 'string','source' => 'string','source_userid' => 'string','source_shopid' => 'string','source_status' => 'string','quantity' => 'string','cancel_quantity' => 'string','recycled_quantity' => 'string','allocated_quantity' => 'string','templates' => 'string','province' => 'string','city' => 'string','district' => 'string','street' => 'string','detail' => 'string','settlement_code' => 'string','platform_account_id' => 'string','platform_shop_id' => 'string','extended_info' => 'string','created_by' => 'string','updated_by' => 'string','created_at' => 'string','updated_at' => 'string','deleted_at' => 'string',];

    const EXPRESS_COMPANY_STATUS_OPEN = 1; //开启
    const EXPRESS_COMPANY_STATUS_CLOSED = 2; //关闭

    const SOURCE_COMPANY_STATUS_NO = 0; //普通网点
    const SOURCE_COMPANY_STATUS_YES = 1; //授权虚拟分享网点
    const SOURCE_COMPANY_STATUS_TRIPARTITE = 2; //三方电子面单

    const SOURCE_COMPANY_STATUS_OPEN = 0; //授权分享网点正常
    const SOURCE_COMPANY_STATUS_CLOSED = 1; //授权分享网点冻结

    const INIT_QUANTITY = 0; //面单余额初始值
    const INIT_UNLIMITE_QUANTITY = -1; //面单余额初始值无限量
    //用于快递公司区分
    const TYPE_COMMON = 0; //公用
    const TYPE_PDD = 1;
    const TYPE_TB = 2;
    const TYPE_DY = 3;
    const TYPE_JD = 4;
    const TYPE_KS = 5;
    const TYPE_WX = 6;
    const TYPE_WXSP = 7;
    const TYPE_XHS = 8;
}