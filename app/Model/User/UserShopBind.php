<?php

namespace App\Model\User;

use App\Model\Shop\Shop;
use Hyperf\DbConnection\Model\Model as MineModel;


/**
* Class user_shop_binds
* @property string $id 
* @property string $user_id 
* @property string $shop_id 
* @property string $created_at 
* @property string $updated_at 
* @property string $deleted_at 
*/
class UserShopBind extends MineModel
{
    protected ?string $table = 'user_shop_binds';

    protected array $fillable = ['id','user_id','shop_id','created_at','updated_at','deleted_at'];

    protected array $casts = ['id' => 'string','user_id' => 'string','shop_id' => 'string','created_at' => 'string','updated_at' => 'string','deleted_at' => 'string',];

    public function shop(): \Hyperf\Database\Model\Relations\HasOne
    {
        return $this->hasOne(Shop::class, 'id', 'shop_id');
    }
}