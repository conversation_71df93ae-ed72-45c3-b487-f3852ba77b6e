<?php

namespace App\Model\Goods;

use App\Model\Product\ProductSku;
use Hyperf\DbConnection\Model\Model as MineModel;


/**
* Class goods_product_relations
* @property integer $id
* @property integer $user_id
* @property integer $bind_type  绑定类型 1商品关联 2货源关联
* @property integer $goods_id
* @property integer $goods_sku_id
* @property string $goods_sku_sku_id  平台的sku id
* @property integer $product_id
* @property integer $product_sku_id
* @property string $pick_price  拿货价
* @property integer $created_by  创建者
* @property integer $updated_by  更新者
* @property string $created_at
* @property string $updated_at
* @property string $deleted_at
*/
class GoodsProductRelation extends MineModel
{
    protected ?string $table = 'goods_product_relations';

    protected array $fillable = ['id','user_id','bind_type','goods_id','goods_sku_id','goods_sku_sku_id','product_id','product_sku_id','pick_price','created_by','updated_by','created_at','updated_at','deleted_at',];

    protected array $casts = ['id' => 'integer','user_id' => 'integer','bind_type' => 'integer','goods_id' => 'integer','goods_sku_id' => 'integer','goods_sku_sku_id' => 'string','product_id' => 'integer','product_sku_id' => 'integer','pick_price' => 'float','created_by' => 'integer','updated_by' => 'integer','created_at' => 'string','updated_at' => 'string','deleted_at' => 'string',];

    // 绑定类型 1商品关联 2货源关联
    const BIND_TYPE_GOODS = 1;
    const BIND_TYPE_PRODUCT_SOURCE = 2;

//    /**
//     * 关联商品规格
//     */
//    public function goodsSku(): \Hyperf\Database\Model\Relations\BelongsTo
//    {
//        return $this->belongsTo(GoodsSku::class, 'goods_sku_id', 'id');
//    }
//
//    /**
//     * 关联货品规格
//     */
//    public function productSku(): \Hyperf\Database\Model\Relations\BelongsTo
//    {
//        return $this->belongsTo(ProductSku::class, 'product_sku_id', 'id');
//    }
}