<?php

declare(strict_types=1);
/**
 * This file is part of MineAdmin.
 *
 * @link     https://www.mineadmin.com
 * @document https://doc.mineadmin.com
 * @contact  <EMAIL>
 * @license  https://github.com/mineadmin/MineAdmin/blob/master/LICENSE
 */

namespace App\Model\Enums\User;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

#[Constants]
enum Type: int
{
    use EnumConstantsTrait;

    #[Message('user.enums.type.100')]
    case SYSTEM = 100;

    #[Message('user.enums.type.200')]
    case USER = 200;

    #[Message('user.enums.type.300')] // 子账号
    case CHILDREN = 300;
}
