<?php

declare(strict_types=1);

namespace App\Command;

use App\Constants\RedisKeyConst;
use App\Dao\Shop\ShopDao;
use App\Dao\SystemAddressDao;
use App\Model\Base\SystemAddress;
use App\Service\Base\SystemAddressService;
use App\Utils\Container;
use Hyperf\Command\Annotation\Command;
use Hyperf\Command\Command as HyperfCommand;
use Hyperf\Contract\StdoutLoggerInterface;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Filesystem\FilesystemFactory;
use Hyperf\Utils\Codec\Json;
use Psr\Container\ContainerInterface;

#[Command]
class SaveSystemAddressCommand extends HyperfCommand
{
//    #[Inject]
//    protected ShopDao $shopDao;
//    #[Inject]
//    protected SystemAddressDao $systemAddressDao;
    #[Inject]
    protected SystemAddressService $systemAddressService;
//    #[Inject]
//    protected FilesystemFactory $filesystemFactory;
    #[Inject]
    protected StdoutLoggerInterface $logger;

    public function __construct(protected ContainerInterface $container)
    {
        parent::__construct('save:system_address_sys');
    }

    public function configure()
    {
        parent::configure();
        $this->setDescription('同步系统地址');
    }

    public function handle()
    {
//        $redis = Container::getRedis();
//        $item = [
//            'code' => 1, 'name' => '国内', 'parent_code' => 0, 'level' => SystemAddress::LEVEL_COUNTRY,
//        ];
//        $this->systemAddressDao->updateOrCreateByCodeAndParentCode(SystemAddress::TYPE_SYS, $item['code'], $item['parent_code'], $item);
//        $oss = $this->filesystemFactory->get('oss');

        $data = json_decode(file_get_contents(BASE_PATH . '/storage/address/pca-code.json'), true);
        $addressList = $this->systemAddressService->formatJsonAddress(SystemAddress::TYPE_SYS, $data);
        $this->systemAddressService->batchSaveAddress(SystemAddress::TYPE_SYS, $addressList);

//        $redis->del(RedisKeyConst::SYSTEM_ADDRESS_SYS_LIST_LV123);
//
//        [$list,] = $this->systemAddressService->queryListBySys();
//
//        $this->logger->info('list：'.count($list));
//        $fileName = config('app.serviceCode') . '/system_address.js';
////        $fileName = 'dkd/address.js'; //懂快递
//        $json = Json::encode($list);
//        $oss->write($fileName, "window.SYS_ADDRESS=$json");
        $this->logger->info('done');

    }
}
