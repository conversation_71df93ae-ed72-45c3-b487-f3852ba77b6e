<?php

namespace App\Http\Admin\Request\User;

use Hyperf\Validation\Request\FormRequest;


class UserRelationRequest extends FormRequest
{

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [                                                                                                                                                                                                                                                                                        
        ];
    }

    public function attributes(): array
    {
        return ['id' => 'id','user_id' => '','related_user_id' => '','relation_type' => '关系的类型：1厂家 2商家','status' => '绑定状态：0 待确认 1 正常 2 已取消','name' => '备注名称','created_by' => '创建者','updated_by' => '更新者','created_at' => '','updated_at' => '',];
    }

}