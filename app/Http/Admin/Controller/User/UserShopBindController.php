<?php

namespace App\Http\Admin\Controller\User;

use App\Http\Admin\Request\User\UserShopBindBindCallbackRequest;
use App\Http\Admin\Request\User\UserShopBindBindUrlRequest;
use App\Service\User\UserShopBindService as Service;
use App\Http\Admin\Request\User\UserShopBindRequest as Request;
use App\Http\Admin\Controller\AbstractController;
use App\Http\Common\Middleware\AccessTokenMiddleware;
use App\Http\Common\Result;
use App\Http\CurrentUser;
use App\Http\Admin\Middleware\PermissionMiddleware;
use Hyperf\Swagger\Annotation\JsonContent;
use Hyperf\Swagger\Annotation\QueryParameter;
use Mine\Access\Attribute\Permission;
use Hyperf\HttpServer\Annotation\Middleware;

//use App\Annotation\ApiName;
//use App\Http\Common\Middleware\OperationHyperfRouterAnnotationMiddleware;
use App\Http\Common\Middleware\OperationMiddleware;
use Hyperf\Swagger\Annotation as OA;
use Mine\Swagger\Attributes\ResultResponse;
use Hyperf\Swagger\Annotation\Post;
use Hyperf\Swagger\Annotation\Put;
use Hyperf\Swagger\Annotation\Get;
use Hyperf\Swagger\Annotation\Delete;
use OpenApi\Attributes\RequestBody;

//use Hyperf\HttpServer\Annotation\Controller;
//use Hyperf\HttpServer\Annotation\RequestMapping;

#[OA\Tag('{用户店铺绑定}')]
#[OA\HyperfServer('http')]
//#[Controller(prefix: '/admin')]
#[Middleware(middleware: AccessTokenMiddleware::class, priority: 100)]
#[Middleware(middleware: PermissionMiddleware::class, priority: 99)]
#[Middleware(middleware: OperationMiddleware::class, priority: 98)]
//#[Middleware(middleware: OperationHyperfRouterAnnotationMiddleware::class, priority: 98)]
final class UserShopBindController extends AbstractController
{
    public function __construct(
        private readonly Service     $service,
        private readonly CurrentUser $currentUser
    )
    {
    }

    #[Get(
        path: '/admin/user/user_shop_bind/list',
        operationId: 'user:user_shop_bind:list',
        summary: '用户店铺绑定列表',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['用户店铺绑定'],
    )]
//    #[ApiName(name: '用户店铺绑定列表')]
//    #[RequestMapping(path: 'user/user_shop_bind/list', methods:"get")]
    #[Permission(code: 'user:user_shop_bind:list')]
    public function pageList(): Result
    {
        return $this->success(
            $this->service->list(
                $this->currentUser,
                $this->getRequestData(),
                $this->getCurrentPage(),
                $this->getPageSize()
            )
        );
    }


//    #[Post(
//        path: '/admin/user/user_shop_bind',
//        operationId: 'user:user_shop_bind:create',
//        summary: '新增用户店铺绑定',
//        security: [['Bearer' => [], 'ApiKey' => []]],
//        tags: ['用户店铺绑定'],
//    )]
//    #[ResultResponse(instance: new Result())]
////    #[ApiName(name: '新增用户店铺绑定')]
////    #[RequestMapping(path: 'user/user_shop_bind', methods:"post")]
//    #[Permission(code: 'user:user_shop_bind:create')]
//    public function create(Request $request): Result
//    {
//        $this->service->create(array_merge($request->validated(), [
//            'created_by' => $this->currentUser->id(),
//        ]));
//        return $this->success();
//    }
//
//    #[Put(
//        path: '/admin/user/user_shop_bind/{id}',
//        operationId: 'user:user_shop_bind:update',
//        summary: '保存用户店铺绑定',
//        security: [['Bearer' => [], 'ApiKey' => []]],
//        tags: ['用户店铺绑定'],
//    )]
//    #[ResultResponse(instance: new Result())]
////    #[ApiName(name: '保存用户店铺绑定')]
////    #[RequestMapping(path: 'user/user_shop_bind/{id}', methods:"put")]
//    #[Permission(code: 'user:user_shop_bind:update')]
//    public function save(int $id, Request $request): Result
//    {
//        $this->service->updateById($id, array_merge($request->validated(), [
//            'updated_by' => $this->currentUser->id(),
//        ]));
//        return $this->success();
//    }

    #[Delete(
        path: '/admin/user/user_shop_bind/{id}',
        operationId: 'user:user_shop_bind:delete',
        summary: '删除用户店铺绑定',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['用户店铺绑定'],
    )]
    #[ResultResponse(instance: new Result())]
//    #[ApiName(name: '删除用户店铺绑定')]
//    #[RequestMapping(path: 'user/user_shop_bind', methods:"delete")]
    #[Permission(code: 'user:user_shop_bind:delete')]
    public function delete(int $id): Result
    {
        $this->service->checkByUserId($id, $this->currentUser->id());
        $this->service->deleteById($id);
        return $this->success();
    }

    #[Get(
        path: '/admin/user/user_shop_bind/bindUrl',
        operationId: 'user:user_shop_bind:bindUrl',
        summary: '获取店铺绑定链接',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['用户店铺绑定'],
    )]
    #[QueryParameter(name: 'platform', description: '平台名称', required: true, rules: 'required|string')]
    #[ResultResponse(instance: new Result())]
    #[Permission(code: 'user:user_shop_bind:bindUrl')]
    public function bindUrl(UserShopBindBindUrlRequest $request): Result
    {
        $newRequest = $request->validatedAndAssignNew();
        $data = $this->service->bindUrl($newRequest, $this->currentUser);
        return $this->success($data);
    }

    #[Post(
        path: '/admin/user/user_shop_bind/bindCallback',
        operationId: 'user:user_shop_bind:bindCallback',
        summary: '店铺绑定回调',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['用户店铺绑定'],
    )]
    #[RequestBody(content: new JsonContent(ref: UserShopBindBindCallbackRequest::class, title: '店铺绑定回调'))]
    #[ResultResponse(instance: new Result())]
    #[Permission(code: 'user:user_shop_bind:bindCallback')]
    public function bindCallback(UserShopBindBindCallbackRequest $request): Result
    {
        $newRequest = $request->validatedAndAssignNew();
        $data = $this->service->bindCallback($newRequest, $this->currentUser);
        return $this->success();
    }
}
