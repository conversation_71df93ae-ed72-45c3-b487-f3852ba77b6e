<?php

namespace App\Http\Admin\Controller\Base;

use App\Model\Base\SystemAddress;
use App\Service\Base\SystemAddressService as Service;
use App\Http\Admin\Request\Base\SystemAddressRequest as Request;
use App\Http\Admin\Controller\AbstractController;
use App\Http\Common\Middleware\AccessTokenMiddleware;
use App\Http\Common\Result;
use App\Http\CurrentUser;
use App\Http\Admin\Middleware\PermissionMiddleware;
use Mine\Access\Attribute\Permission;
use Hyperf\HttpServer\Annotation\Middleware;

//use App\Annotation\ApiName;
//use App\Http\Common\Middleware\OperationHyperfRouterAnnotationMiddleware;
use App\Http\Common\Middleware\OperationMiddleware;
use Hyperf\Swagger\Annotation as OA;
use Mine\Swagger\Attributes\ResultResponse;
use Hyperf\Swagger\Annotation\Post;
use Hyperf\Swagger\Annotation\Put;
use Hyperf\Swagger\Annotation\Get;
use Hyperf\Swagger\Annotation\Delete;

//use Hyperf\HttpServer\Annotation\Controller;
//use Hyperf\HttpServer\Annotation\RequestMapping;

#[OA\Tag('{系统地址}')]
#[OA\HyperfServer('http')]
//#[Controller(prefix: '/admin')]
#[Middleware(middleware: AccessTokenMiddleware::class, priority: 100)]
#[Middleware(middleware: PermissionMiddleware::class, priority: 99)]
#[Middleware(middleware: OperationMiddleware::class, priority: 98)]
//#[Middleware(middleware: OperationHyperfRouterAnnotationMiddleware::class, priority: 98)]
final class SystemAddressController extends AbstractController
{
    public function __construct(
        private readonly Service     $service,
        private readonly CurrentUser $currentUser
    )
    {
    }

    #[Get(
        path: '/admin/base/system_address/list',
        operationId: 'base:system_address:list',
        summary: '系统地址列表',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['系统地址'],
    )]
//    #[ApiName(name: '系统地址列表')]
//    #[RequestMapping(path: 'base/system_address/list', methods:"get")]
    #[Permission(code: 'base:system_address:list')]
    public function list(): Result
    {
        $params = $this->getRequestData();
        $params['type'] = SystemAddress::TYPE_SYS;
        return $this->success(
            $this->service->getSimpleList($params),
        );
    }


//    #[Post(
//        path: '/admin/base/system_address',
//        operationId: 'base:system_address:create',
//        summary: '新增系统地址',
//        security: [['Bearer' => [], 'ApiKey' => []]],
//        tags: ['系统地址'],
//    )]
//    #[ResultResponse(instance: new Result())]
////    #[ApiName(name: '新增系统地址')]
////    #[RequestMapping(path: 'base/system_address', methods:"post")]
//    #[Permission(code: 'base:system_address:create')]
//    public function create(Request $request): Result
//    {
//        $this->service->create(array_merge($request->validated(), [
//            'created_by' => $this->currentUser->id(),
//        ]));
//        return $this->success();
//    }
//
//    #[Put(
//        path: '/admin/base/system_address/{id}',
//        operationId: 'base:system_address:update',
//        summary: '保存系统地址',
//        security: [['Bearer' => [], 'ApiKey' => []]],
//        tags: ['系统地址'],
//    )]
//    #[ResultResponse(instance: new Result())]
////    #[ApiName(name: '保存系统地址')]
////    #[RequestMapping(path: 'base/system_address/{id}', methods:"put")]
//    #[Permission(code: 'base:system_address:update')]
//    public function save(int $id, Request $request): Result
//    {
//        $this->service->updateById($id, array_merge($request->validated(), [
//            'updated_by' => $this->currentUser->id(),
//        ]));
//        return $this->success();
//    }
//
//
//    #[Delete(
//        path: '/admin/base/system_address',
//        operationId: 'base:system_address:delete',
//        summary: '删除系统地址',
//        security: [['Bearer' => [], 'ApiKey' => []]],
//        tags: ['系统地址'],
//    )]
//    #[ResultResponse(instance: new Result())]
////    #[ApiName(name: '删除系统地址')]
////    #[RequestMapping(path: 'base/system_address', methods:"delete")]
//    #[Permission(code: 'base:system_address:delete')]
//    public function delete(): Result
//    {
//        $this->service->deleteById($this->getRequestData());
//        return $this->success();
//    }

}
