<?php

namespace App\Http\Admin\Controller\Product;

use App\Http\Admin\Request\IdRequest;
use App\Service\Product\PickTaskCodeService as Service;
use App\Http\Admin\Request\Product\PickTaskCodeRequest as Request;
use App\Http\Admin\Controller\AbstractController;
use App\Http\Common\Middleware\AccessTokenMiddleware;
use App\Http\Common\Result;
use App\Http\CurrentUser;
use App\Http\Admin\Middleware\PermissionMiddleware;
use Mine\Access\Attribute\Permission;
use Hyperf\HttpServer\Annotation\Middleware;

//use App\Annotation\ApiName;
//use App\Http\Common\Middleware\OperationHyperfRouterAnnotationMiddleware;
use App\Http\Common\Middleware\OperationMiddleware;
use Hyperf\Swagger\Annotation as OA;
use Mine\Swagger\Attributes\ResultResponse;
use Hyperf\Swagger\Annotation\Post;
use Hyperf\Swagger\Annotation\Put;
use Hyperf\Swagger\Annotation\Get;
use Hyperf\Swagger\Annotation\Delete;

//use Hyperf\HttpServer\Annotation\Controller;
//use Hyperf\HttpServer\Annotation\RequestMapping;

#[OA\Tag('{拿货任务编码}')]
#[OA\HyperfServer('http')]
//#[Controller(prefix: '/admin')]
#[Middleware(middleware: AccessTokenMiddleware::class, priority: 100)]
#[Middleware(middleware: PermissionMiddleware::class, priority: 99)]
#[Middleware(middleware: OperationMiddleware::class, priority: 98)]
//#[Middleware(middleware: OperationHyperfRouterAnnotationMiddleware::class, priority: 98)]
final class PickTaskCodeController extends AbstractController
{
    public function __construct(
        private readonly Service     $service,
        private readonly CurrentUser $currentUser
    )
    {
    }

//    #[Get(
//        path: '/admin/product/pick_task_code/list',
//        operationId: 'product:pick_task_code:list',
//        summary: '拿货任务编码列表',
//        security: [['Bearer' => [], 'ApiKey' => []]],
//        tags: ['拿货任务编码'],
//    )]
////    #[ApiName(name: '拿货任务编码列表')]
////    #[RequestMapping(path: 'product/pick_task_code/list', methods:"get")]
//    #[Permission(code: 'product:pick_task_code:list')]
//    public function pageList(): Result
//    {
//        return $this->success(
//            $this->service->page(
//                $this->getRequestData(),
//                $this->getCurrentPage(),
//                $this->getPageSize()
//            )
//        );
//    }
//
//
//    #[Post(
//        path: '/admin/product/pick_task_code',
//        operationId: 'product:pick_task_code:create',
//        summary: '新增拿货任务编码',
//        security: [['Bearer' => [], 'ApiKey' => []]],
//        tags: ['拿货任务编码'],
//    )]
//    #[ResultResponse(instance: new Result())]
////    #[ApiName(name: '新增拿货任务编码')]
////    #[RequestMapping(path: 'product/pick_task_code', methods:"post")]
//    #[Permission(code: 'product:pick_task_code:create')]
//    public function create(Request $request): Result
//    {
//        $this->service->create(array_merge($request->validated(), [
//            'created_by' => $this->currentUser->id(),
//        ]));
//        return $this->success();
//    }
//
//    #[Put(
//        path: '/admin/product/pick_task_code/{id}',
//        operationId: 'product:pick_task_code:update',
//        summary: '保存拿货任务编码',
//        security: [['Bearer' => [], 'ApiKey' => []]],
//        tags: ['拿货任务编码'],
//    )]
//    #[ResultResponse(instance: new Result())]
////    #[ApiName(name: '保存拿货任务编码')]
////    #[RequestMapping(path: 'product/pick_task_code/{id}', methods:"put")]
//    #[Permission(code: 'product:pick_task_code:update')]
//    public function save(int $id, Request $request): Result
//    {
//        $this->service->updateById($id, array_merge($request->validated(), [
//            'updated_by' => $this->currentUser->id(),
//        ]));
//        return $this->success();
//    }
//
//
//    #[Delete(
//        path: '/admin/product/pick_task_code',
//        operationId: 'product:pick_task_code:delete',
//        summary: '删除拿货任务编码',
//        security: [['Bearer' => [], 'ApiKey' => []]],
//        tags: ['拿货任务编码'],
//    )]
//    #[ResultResponse(instance: new Result())]
//    #[RequestBody(content: new JsonContent(ref: IdRequest::class, title: '删除拿货任务编码'))]
////    #[ApiName(name: '删除拿货任务编码')]
////    #[RequestMapping(path: 'product/pick_task_code', methods:"delete")]
//    #[Permission(code: 'product:pick_task_code:delete')]
//    public function delete(IdRequest $request): Result
//    {
//        $newRequest = $request->validatedAndAssign();
//        $this->service->deleteByIdAndUser($this->currentUser->user(), $newRequest->id);
//        return $this->success();
//    }

}
