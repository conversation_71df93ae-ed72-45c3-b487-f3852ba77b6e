<script setup>

import {ElDatePicker, ElInput} from "element-plus";
import {useMessage} from "@/hooks/useMessage.js";
import {addProduct, deleteProduct, getProducts} from "~/base/api/goods.js";

const searchItem = [
  {label: '货品名称', prop: 'productName', render: 'input'},
  {label: '货品规格', prop: 'productsku_name', render: 'input'},
  {
    label: '货品编码', prop: 'productOuterId', render: (model) => {
      return h(ElInput, {
        placeholder: '货品编码/货品规格编码',
        modelValue: model.formData.productOuterId,
        'onUpdate:modelValue': (value) => {
          model.formData.productOuterId = value;
        }
      });
    }
  },
  {
    label: '创建时间', prop: 'createTime', render: (model) => {
      return h(ElDatePicker, {
        type: 'datetimerange',
        'start-placeholder': "创建时间起",
        'end-placeholder': "创建时间止",
        format: "YYYY-MM-DD HH:mm:ss",
        modelValue: model.formData.createTime,
        'onUpdate:modelValue': (value) => {
          model.formData.createTime = value;
        }
      });
    }
  },
]

const {
  getMenuCollapseState
} = useSettingStore()


const pageReq = ref({
  page: 1,
  pageSize: 100,
  total: 2
})

const handleSizeChange = (val) => {
  pageReq.value.pageSize = val
  getData();
}
const handleCurrentChange = (val) => {
  pageReq.value.page = val
  getData();
}

function objectSpanMethod({
                            row,
                            column,
                            rowIndex,
                            columnIndex,
                          }) {
  if ([0, 1, 2, 3, 7].includes(columnIndex)) {
    if (row.rowSpan > 0) {
      return {
        rowspan: row.rowSpan,
        colspan: 1,
      }
    } else {
      return {
        rowspan: 0,
        colspan: 0,
      }
    }
  }
  return {
    rowspan: 1,
    colspan: 1,
  }
}

const searchParams = ref({})

function getData(params) {
  if (!params) {
    params = {...searchParams.value}
  } else {
    searchParams.value = {...params}
  }
  params.page = pageReq.value.page
  params.page_size = pageReq.value.pageSize
  getProducts(params).then(res=>{
    const {list, total} = res.data
    pageReq.value.total = total
    const resList = []
    list.forEach(it => {
      it.skus.forEach((item, idx) => {
        const obj = {...item, rowSpan: idx ? 0 : it.skus.length, ...it, skuChecked: false}
        delete obj.skus
        resList.push(obj)
      })
    })
    tableList.value = resList
  })
}

onMounted(() => {
  getData();
})
const tableList = ref([])

function selectionChange(e) {
}

const modalObj = ref({
  visible: false,
  title: '',
  width: '500px',
  form: {},
  type: '',
})

function createProduct(){
  modalObj.value={
    visible: true,
    title: '新建货品',
    width: '1000px',
    type: 'addProduct',
    form: {skus: [{}]}
  }
}

function addSku(item) {
  modalObj.value.form.skus.push({...item})
}

function delSku(idx) {
  modalObj.value.form.skus.splice(idx, 1)
}

const msg = useMessage()

function judgeData() {
  let flag = true
  const {name, skus} = modalObj.value.form
  if (!name) {
    msg.error("货品名称不能为空")
    flag = false;
    return flag;
  }
  if (!skus?.length) {
    msg.error("货品规格不能为空")
    flag = false;
    return flag;
  }
  const errorList = skus.filter(it => !it.sku_name)
  if (errorList?.length) {
    msg.error("规格名称不能为空")
    flag = false;
    return flag;
  }
  return flag;
}

async function asyncOk() {
  if (!judgeData()) {
    return false;
  }
  const res = await addProduct(modalObj.value.form)
  if (res.code !== 200) {
    return false
  }
  msg.success('创建成功')
  getData()
  modalObj.value.visible = false
}

function delProduct(item) {
  msg.delConfirm(`是否确认删除货品【${item.name}】？`, '删除货品提醒').then(async () => {
    deleteProduct(item.id).then(()=>{
      msg.success('删除成功')
      setTimeout(()=>{
        getData()
      },500)
    })
  })
}
</script>


<template>
  <div class="product-content">
    <div class="search-content">
      <maSearch
        :options="{ showButton: true }"
        :form-options="{ labelWidth: '80px'}"
        :search-items="searchItem"
        @search="getData"
      />
    </div>
    <div class="order-table">
      <el-button type="primary" @click="createProduct">新建货品</el-button>
      <el-table :data="tableList" :span-method="objectSpanMethod" @selection-change="selectionChange" class="mt-2">
        <el-table-column type="selection"/>
        <el-table-column align="center" label="序号" width="60" type="index"/>
        <el-table-column label="货品名称 / 编码">
          <template #default="scope">
            <p>{{ scope.row.name }}</p>
            <p>货品编码：{{ scope.row.product_no || '无' }}</p>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="created_at" width="180"/>
        <el-table-column label="规格信息">
          <template #default="scope">
            <div class="flex_center">
              <el-checkbox v-model="scope.row.skuChecked"/>
              <div class="flex_top ml-2">
                <el-image
                  style="width: 60px; height: 60px;min-width: 60px"
                  :src="scope.row.image_url"
                  :hide-on-click-modal="true"
                  :preview-src-list="[scope.row.image_url]"
                />
                <div class="ml-2">
                  <p>{{ scope.row.sku_name }}</p>
                  <p>规格编码：{{ scope.row.merchant_code || '无' }}</p>
                  <p>条形码：{{ scope.row.barcode || '无' }}</p>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="重量(kg)" width="150">
          <template #default="scope">
            <el-input v-model="scope.row.weight" placeholder="重量">
              <template #append>kg</template>
            </el-input>
<!--            <el-input v-model="scope.row.volume" placeholder="体积" class="mt-2">-->
<!--              <template #append>m³</template>-->
<!--            </el-input>-->
          </template>
        </el-table-column>
        <el-table-column label="成本(元) / 售价(元)" width="150">
          <template #default="scope">
            <el-input v-model="scope.row.cost_price" placeholder="成本">
              <template #append>元</template>
            </el-input>
            <el-input v-model="scope.row.selling_price" placeholder="售价" class="mt-2">
              <template #append>元</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <a>编辑</a>
            <a class="red ml-2" @click="delProduct(scope.row)">删除</a>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="order-footer flex_center"
         :style="{'width': getMenuCollapseState() ? 'calc(100% - var(--mine-g-sub-aside-collapse-width) - 25px)' : 'calc(100% - var(--mine-g-sub-aside-width) - 25px)'}">
      <!--        <div>-->
      <!--          <span>已选</span>-->
      <!--          <span class="order_count">1</span>-->
      <!--          <span>项</span>-->
      <!--        </div>-->
      <div>
        <el-button type="primary">保存当前页修改</el-button>
        <el-button type="danger" plain>批量删除</el-button>
        <el-button>批量设置重量</el-button>
<!--        <el-button>批量设置体积</el-button>-->
        <el-button>批量设置成本</el-button>
        <el-button>批量设置售价</el-button>
      </div>
      <div class="ml-auto">
        <el-pagination
          v-model:current-page="pageReq.page"
          v-model:page-size="pageReq.pageSize"
          :page-sizes="[100, 200, 300, 400]"
          layout="total, sizes, prev, pager, next"
          :total="pageReq.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <el-dialog :title="modalObj.title" v-model="modalObj.visible" :width="modalObj.width" :close-on-click-modal="false">
      <template v-if="modalObj.type==='addProduct'">
        <div>
          <h3>货品信息</h3>
          <div class="flex_center mt-2">
            <span class="label-content">货品名称</span>
            <el-input v-model="modalObj.form.name"/>
          </div>
          <div class="flex_center mt-2">
            <span class="label-content">货品编码</span>
            <el-input v-model="modalObj.form.product_no"/>
          </div>
          <h3 class="mt-5">规格信息</h3>
          <el-table class="mt-2" :data="modalObj.form.skus">
            <el-table-column label="规格图片">
              <template #default="scope">
                <el-upload
                  class="avatar-uploader"
                  action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
                  :show-file-list="false"
                  :on-success="handleAvatarSuccess"
                  :before-upload="beforeAvatarUpload"
                >
                  <img v-if="scope.row.image_url" :src="scope.row.image_url" class="avatar"/>
                  <ma-svg-icon v-else class="avatar-uploader-icon" name="material-symbols:add-2-rounded" :size="20"/>
                </el-upload>
              </template>
            </el-table-column>
            <el-table-column label="规格名称">
              <template #default="scope">
                <el-input v-model="scope.row.sku_name" placeholder="规格名称"/>
              </template>
            </el-table-column>
            <el-table-column label="规格编码">
              <template #default="scope">
                <el-input v-model="scope.row.merchant_code" placeholder="规格编码"/>
              </template>
            </el-table-column>
            <el-table-column label="条形码">
              <template #default="scope">
                <el-input v-model="scope.row.barcode" placeholder="条形码"/>
              </template>
            </el-table-column>
            <el-table-column label="重量">
            <template #default="scope">
              <el-input v-model="scope.row.weight" placeholder="重量">
                <template #append>kg</template>
              </el-input>
<!--              <el-input v-model="scope.row.volume" placeholder="体积" class="mt-2">-->
<!--                <template #append>m³</template>-->
<!--              </el-input>-->
            </template>
          </el-table-column>
            <el-table-column label="成本/售价">
              <template #default="scope">
                <el-input v-model="scope.row.cost_price" placeholder="成本">
                  <template #append>元</template>
                </el-input>
                <el-input v-model="scope.row.selling_price" placeholder="售价" class="mt-2">
                  <template #append>元</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="60">
              <template #default="scope">
                <a @click="addSku(scope.row)">复制</a>
                <a class="red" v-if="modalObj.form.skus.length >1" @click="delSku(scope.$index)">删除</a>
              </template>
            </el-table-column>
          </el-table>
          <a class="mt-2" @click="addSku({})">新增一行</a>
        </div>
      </template>
      <template #footer>
        <el-button @click="modalObj.visible = false">取 消</el-button>
        <el-button type="primary" @click="asyncOk">保 存</el-button>
      </template>
    </el-dialog>
  </div>
</template>


<style lang="scss">
.product-content {
  margin: 0.75rem;

  .search-content {
    padding: 0.75rem;
    background: white;
  }

  .order-table {
    background: white;
    padding: 0.75rem 0.75rem 5rem 0.75rem;
    margin: 0.75rem 0;
  }

  .order-footer {
    height: 60px;
    position: fixed;
    bottom: 0;
    background: white;
    z-index: 111;
    right: 0;
    padding: 0 1rem;
    column-gap: 0.25rem;

    .order_count {
      margin: 0 0.2rem;
      font-size: 20px;
      color: var(--el-color-primary);
    }
  }

  .label-content {
    min-width: 85px;
    max-width: 85px;
  }
}

.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 80px;
  height: 80px;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 80px;
  color: #8c939d;
  text-align: center;
}
</style>
