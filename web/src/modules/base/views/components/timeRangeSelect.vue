<script setup>
const props = defineProps({
  originalTimeField: {type: String, default: 'order_created_at'},
  originalTimeRange: {type: Array, default: []},
  source: {type: String, defalut: 'not-send'}
})

const timeField = ref('order_created_at')

const fieldList = [
    {label: '下单时间', value: 'order_created_at'},
    {label: '付款时间', value: 'pay_at'},
    {label: '打印时间', value: 'printed_at'},
    {label: '发货时间', value: 'send_at'},
    {label: '分配时间', value: 'allocate_at'},
  ]


const timeFieldList = ref([])
const timeRange = ref('')

onMounted(()=>{
  nextTick(()=>{
    if (props.source === 'not-send') {
      timeFieldList.value = fieldList.filter(it => !['send_at', 'allocate_at'].includes(it.value))
    } else if (props.source === 'has-send') {
      timeFieldList.value = fieldList.filter(it => !['allocate_at'].includes(it.value))
    } else {
      timeFieldList.value = [...fieldList]
    }
    if (props.originalTimeField) {
      timeField.value = props.originalTimeField
    }
    if (props.originalTimeRange?.length) {
      timeRange.value = props.originalTimeRange
    }
  })
})

const dayMillis = 86400000;

function getTimeRange(start, timeLength) {
  const end = new Date(start.getTime() + timeLength)
  return [start, end];
}

function getDateDayStart(date = new Date(), subDay = 0) {
  const year = date.getFullYear(), month = date.getMonth(), day = date.getDate() - subDay;
  return new Date(year, month, day);
}

function getLastMonth() {
  const date = new Date();
  const firstDate = new Date(date.getFullYear(), date.getMonth() - 1, 1);
  const day = new Date(date.getFullYear(), date.getMonth(), 0).getDate();
  const endDate = new Date(new Date(date.getFullYear(), date.getMonth() - 1, day).getTime() + dayMillis - 1);
  return [firstDate, endDate]
}

const shortcuts = [
  {
    text: '今天',
    value: () => {
      return getTimeRange(getDateDayStart(), dayMillis - 1);
    },
  },
  {
    text: '昨天',
    value: () => {
      return getTimeRange(getDateDayStart(new Date(), 1), dayMillis - 1);
    },
  },
  {
    text: '最近3天',
    value: () => {
      return getTimeRange(getDateDayStart(new Date(), 2), 3 * dayMillis - 1);
    },
  },
  {
    text: '7天',
    value: () => {
      return getTimeRange(getDateDayStart(new Date(), 6), 7 * dayMillis - 1);
    },
  },
  {
    text: '14天',
    value: () => {
      return getTimeRange(getDateDayStart(new Date(), 13), 14 * dayMillis - 1);
    },
  },
  {
    text: '30天',
    value: () => {
      return getTimeRange(getDateDayStart(new Date(), 29), 30 * dayMillis - 1);
    },
  },
  {
    text: '上个月',
    value: () => {
     return getLastMonth()
    },
  },
]

const emit = defineEmits(['update:modelValue']);


function changeField(e) {
  emit('update:modelValue', {timeField: e, timeRange: timeRange.value});
}

function changeTime(e) {
  emit('update:modelValue', {timeField: timeField.value, timeRange: e});
}
</script>

<template>
  <div class="flex_center time-range-search">
    <el-select v-model="timeField" style="width: 120px" @change="changeField">
      <el-option v-for="it in timeFieldList" :key="it.value" :label="it.label" :value="it.value"/>
    </el-select>
    <el-date-picker
      @change="changeTime"
      v-model="timeRange"
      type="datetimerange"
      :shortcuts="shortcuts"
      range-separator="——"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
    />
  </div>

</template>

<style lang="scss">
.time-range-search{
  max-width: 100%;
  .el-select__wrapper {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  .el-input__wrapper {
    width: 100px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left: none;
  }
}
</style>
