<template>
  <div class="address-select">
    <el-cascader v-model="value2" :placeholder="text" :options="rootList" :props="prop" :disabled="disable" @change="change"/>
  </div>
</template>

<script lang="ts" setup>
import {onMounted, ref, watch} from 'vue';
import {getCascaderAddress} from '@/utils/address';

const props = defineProps({
  modelValue: Array,
  disable: {type: Boolean, default: false},
  text: String,
  checkStrictly: {type: Boolean, default: false},
});

const cascaderAddressList = getCascaderAddress();

const value2 = ref(props.modelValue);
const rootList = ref([]);

watch([() => props.modelValue], () => {
  if (!props.checkStrictly && props.modelValue?.length === 3) {
    // initRange(props.modelValue);
    value2.value = props.modelValue;
  }
});

function clearVal() {
  value2.value = null
}

defineExpose({clearVal});

const emit = defineEmits(['update:modelValue', 'change']);

function change($event) {
  emit('update:modelValue', $event);
  emit('change', $event);
}

const prop = {
  lazy: true,
    expandTrigger: 'hover',
    checkStrictly: props.checkStrictly,
    lazyLoad(item, callback) {
      loadData(item.data,callback)
    },
  }

  function loadData(item, callback) {
    let nextChildren = null;
    if (item.level === 1) {
      const menu = cascaderAddressList.find((it) => it.value === item.value);
      nextChildren = getLevelArr(menu.children, false);
    } else if (item.level === 2) {
      const menu = cascaderAddressList
        .find((it) => it.code === item.parent_code)
        .children.find((it) => it.value === item.value);
      nextChildren = getLevelArr(menu.children, true);
    }
    item.children = nextChildren;
    setTimeout(() => {
      callback(nextChildren);
    }, 10);
  }

  function getLevelArr(arrIn, isLeaf) {
    const arr = [];
    [...arrIn].forEach((it) => {
      const it2 = { ...it,leaf: isLeaf };
      delete it2.children;
      if (!isLeaf) {
        it2.loading = false;
      }
      arr.push(it2);
    });
    return arr;
  }

  function initRange(address) {
    if (address && address.length > 0) {
      const first = address[0];
      let array = cascaderAddressList.map((it) => {
          return { ...it };
        }),
        firstIndex = rootList.value.findIndex((it) => it.value === first);
      const menu = array.find((it) => it.value === first);
      rootList.value.splice(firstIndex, 1, menu);
    }
  }

  onMounted(() => {
    rootList.value = getLevelArr(cascaderAddressList, false);
    initRange([]);
  });
</script>

<style lang="scss">
  .address-select {
    width: 100%;
  }
</style>
