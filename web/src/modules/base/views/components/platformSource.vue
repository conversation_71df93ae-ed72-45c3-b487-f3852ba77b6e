<script setup>
const props = defineProps({
  modelValue: {
    type: [Number, String],
    default: null,
  },
  placeholder: {
    type: String,
    default: '订单来源',
  },
  disabled: {
    type: Boolean,
    defalut: false
  }
});
const platformList = [
  {label:'自定义店铺',value:0,name:'self'},
  {label:'淘宝',value:1,name:'taobao_top'},
  {label:'拼多多',value:2,name:'pdd'},
  {label:'快手',value:3,name:'ks'},
  {label:'京东',value:4,name:'jd'},
  {label:'抖音',value:5,name:'dy'},
  {label:'微信',value:7,name:'wxsp'},
  {label:'小红书',value:8,name:'xhs'},
  {label:'阿里巴巴',value:9,name:'albb'},
  {label:'淘工厂',value:10,name:'c2m'}
]

const emit = defineEmits(['update:modelValue']);

const platform = computed({
  get() {
    return props.modelValue;
  },
  set(v) {
    emit('update:modelValue', v);
  },
});
</script>


<template>
  <el-select v-model="platform" :placeholder="props.placeholder" :disabled="disabled">
    <el-option v-for="it in platformList" :key="it.value" :value="it.value" :label="it.label"/>
  </el-select>
</template>
