<script setup>

const activeMode = ref(1)
const orderList = ref([{
  id: 1,
  source: '【淘宝】1988sunjia',
  tid: '2592762783249730353',
  receiverInfo: '卖**，***********，福建省泉州市南安市官*镇**村洋行**号[****]',
  createTime: '2025-06-03 08:20:37',
  payTime: '2025-06-03 08:30:37',
  lastShipTime: '2025-06-05 08:30:37',
  goods: [
    {
      skuId: 1,
      goodsImg: 'https://img.alicdn.com/bao/uploaded/i2/75758527/O1CN01tnb9a12CrQJKs5ygk_!!75758527.jpg',
      goodsName: 'A104针织真丝背心女夏打底外穿百搭弹力修身显瘦性感蕾丝无袖吊带；白色,XL',
      quantity: 1,
      price: 208.5
    },
    {
      skuId: 2,
      goodsImg: 'https://img.alicdn.com/bao/uploaded/i2/75758527/O1CN01WAbBUv2CrQJHVRFXK_!!75758527.jpg',
      goodsName: '灰底紫花【N517福利375-C168】100桑蚕丝印花圆领中袖上衣；灰底紫花,2XL[125-135斤]',
      quantity: 2,
      price: 1005
    },
    {
      skuId: 3,
      goodsImg: 'https://img.alicdn.com/bao/uploaded/i1/75758527/TB24ifGs1GSBuNjSspbXXciipXa_!!75758527.jpg',
      goodsName: 'J74【T521直播福利】里层100桑蚕丝纯色细边圆领短袖上衣；白色,L[115斤以下]',
      quantity: 1,
      price: 150
    },
    {
      skuId: 4,
      goodsImg: 'https://img.alicdn.com/bao/uploaded/i2/75758527/O1CN01pFn9Ud2CrQJEYoVl2_!!75758527.jpg',
      goodsName: 'J43灰紫色【517福利】里层100桑蚕丝外层莫代尔纯色长裤直筒裤；灰紫色,XL[115-130斤]',
      quantity: 1,
      price: 300
    }
  ],
  mergeOrders: [
    {
      tid: '4364456294848097429',
      createTime: '2025-06-02 16:01:29',
      payTime: '2025-06-02 16:22:29',
      lastShipTime: '2025-06-04 16:22:29',
      goods: [
        {
          skuId: 3,
          goodsImg: 'https://img.alicdn.com/bao/uploaded/i1/75758527/TB24ifGs1GSBuNjSspbXXciipXa_!!75758527.jpg',
          goodsName: 'J74【T521直播福利】里层100桑蚕丝纯色细边圆领短袖上衣；白色,L[115斤以下]',
          quantity: 2,
          price: 300
        },
        {
          skuId: 5,
          goodsImg: 'https://img.alicdn.com/bao/uploaded/i2/75758527/O1CN01QwRL7P2CrQJTJkmhd_!!75758527.jpg',
          goodsName: '蓝色花【N531福利W999】100桑蚕丝网纱圆领中袖上衣；蓝色花,L[115斤以下]',
          quantity: 1,
          price: 500
        }
      ]
    }
  ]
}])
const columns = [
  {type: "selection", width: "55"},
  {type: "expand", width: "40"},
  {label: '订单来源', prop: 'source', showOverflowTooltip: false},
  {label: '订单号', prop: 'tid', showOverflowTooltip: false},
  {label: '收件信息', prop: 'receiverInfo', showOverflowTooltip: false},
  {label: '留言备注', prop: 'remark', showOverflowTooltip: false},
  {label: '物流公司', prop: 'company', showOverflowTooltip: false},
  {label: '运单号', prop: 'waybillCode', showOverflowTooltip: false},
  {label: '商品', prop: 'goods', showOverflowTooltip: false, minWidth: 200},
  {label: '下单时间', prop: 'createTime', showOverflowTooltip: false},
  {label: '承诺揽收时间', prop: 'lastShipTime', showOverflowTooltip: false},
  {label: '操作', prop: 'opt', showOverflowTooltip: false, width: 120}
]
const expandedRows = ref([])

function getData() {
  orderList.value.forEach(it => {
    const goodsList = it.goods.map(i => {
      return {...i}
    })
    const orders = [{tid: it.tid, goods: it.goods, payTime: it.payTime, lastShipTime: it.lastShipTime}]
    if (it.mergeOrders?.length) {
      it.mergeOrders.forEach(i => {
        orders.push({tid: i.tid, goods: i.goods, payTime: i.payTime, lastShipTime: i.lastShipTime})
        i.goods.forEach(good => {
          const match = goodsList.find(item => item.skuId === good.skuId)
          if (match) {
            match.quantity += good.quantity
          } else {
            goodsList.push({...good})
          }
        })
      })
    }
    it.goodsList = goodsList
    it.orders = orders
  })
}

function getTotalAmount(item) {
  console.log(11111, item)
  let amount = 0;
  item.goods.forEach(it => {
    amount += it.price
  })
  return amount;
}

const pageReq = ref({
  page: 1,
  pageSize: 100,
  total: 1
})

const handleSizeChange = (val) => {
  console.log(`${val} items per page`)
}
const handleCurrentChange = (val) => {
  console.log(`current page: ${val}`)
}

const tableRef = ref()

const toggleExpand = (row) => {
  if (expandedRows.value.includes(row.id)) {
    expandedRows.value = expandedRows.value.filter(it => it !== row.id)
  } else {
    expandedRows.value.push(row.id)
  }
  tableRef.value.toggleRowExpansion(row);
};

onMounted(() => {
  getData()
})

const sortList = [
  {value: "order_created_time", label: "下单时间", order: "desc"},
  {value: "pay_time", label: "付款时间", order: "desc"},
  {value: "promise_ship_at", label: "承诺发货时间", order: "desc"},
  {value: "take_waybill_at", label: "取号时间", order: "desc"},
  {value: "printed_time", label: "打印时间", order: "desc"},
  {value: "goods_count", label: "商品数量", order: "desc"},
  {value: "goods_price", label: "商品金额", order: "desc"},
  {value: "goods_title", label: "商品名称", order: "asc"},
  {value: "custom_title", label: "商品简称", order: "asc"},
  {value: "outer_iid", label: "商品编码", order: "asc"},
  {value: "sku_value", label: "规格名称", order: "asc"},
  {value: "custom_sku_value", label: "规格简称", order: "asc"},
  {value: "outer_sku_iid", label: "规格编码", order: "asc"},
  {value: "area", label: "省份城市", order: "desc"}
]

const sortColumn = ref({
  field: 'order_created_time',
  sort: 'desc'
})


function changeMode() {

}

const modalObj = ref({
  visible: false,
  title: '',
  width: '30%',
  form: {},
  type: '',
})

function asyncOk() {

}

function setAllocationRule() {
  modalObj.value = {
    visible: true,
    title: '自动分配设置',
    width: '30%',
    form: {},
    type: 'setAllocation',
  }
}

const shopList = ref([{shopName: "麦町日淘", platform: '抖音'}])
const shopBindColumns = [
  {label: '序号', prop: 'index'},
  {label: '平台', prop: 'platform'},
  {label: '店铺名称', prop: 'shopName'},
  {label: '厂家', prop: 'factory'},
]

function setShopBindFactory() {
  modalObj.value = {
    visible: true,
    title: '店铺绑定厂家',
    width: '50%',
    form: {},
    type: 'shopBindFactory',
  }
}

const goodsList = ref([
  {
    platform: '抖音',
    goodsName: '天然室内印尼水沉香盘香 家用卧室2小时安神助眠熏香',
    skuName: '1盒;'
  }, {
    platform: '抖音',
    goodsName: '1现货日版CHAMPION冠军潮牌双肩背包男女小清新情侣款翻盖书包17S',
    skuName: '香槟色;'
  }, {
    platform: '抖音',
    goodsName: '1现货日版CHAMPION冠军潮牌双肩背包男女小清新情侣款翻盖书包17S',
    skuName: '浅紫色;'
  }, {
    platform: '抖音',
    goodsName: '1现货日版CHAMPION冠军潮牌双肩背包男女小清新情侣款翻盖书包17S',
    skuName: '紫罗兰;'
  }])
const excludeGoodsColumns = [
  {label: '平台', prop: 'platform'},
  {label: '商品名称', prop: 'goodsName'},
  {label: '规格名称', prop: 'skuName'},
  {label: '操作', prop: 'opt'},
]

function removeGoods(idx) {
  goodsList.value.splice(idx, 1)
}

function setExcludeGoodsRule() {
  modalObj.value = {
    visible: true,
    title: '开启整店推送，设置部分商品不推送',
    width: '70%',
    form: {},
    type: 'excludeGoodsRule',
  }
}

const mergeOrderFlag = ref(true)

function setMergeOrderRule() {
  modalObj.value = {
    visible: true,
    title: '自动合单',
    width: '30%',
    form: {},
    type: 'mergeOrderSet',
  }
}

const {
  getMenuCollapseState
} = useSettingStore()
</script>

<template>
  <div class="print-send-order">
    <el-tabs v-model="activeMode" @tab-change="changeMode" class="tab-select">
      <el-tab-pane label="待发货" :name="1"/>
      <el-tab-pane label="已发货" :name="2"/>
      <el-tab-pane label="特殊订单" :name="3"/>
      <el-tab-pane label="异常订单" :name="4"/>
    </el-tabs>
    <div class="opt-button">
      <el-button type="primary">同步订单</el-button>
      <el-button type="primary" class="ml-1">新建订单</el-button>
    </div>

    <div class="order-table">
      <div class="order-opt-content flex_center">
        <div class="ml-auto flex_center">
          <a @click="setAllocationRule">自动分配设置</a>
          <a class="ml-8" @click="setMergeOrderRule">合单设置</a>
          <el-select class="ml-8" style="width: 140px" v-model="sortColumn.field">
            <el-option v-for="it in sortList" :key="it.value" :label="it.label" :value="it.value"/>
          </el-select>
          <ma-svg-icon name="material-symbols:arrow-upward" size="20" class="sort_item ml-1"
                       :class="{'sort_checked': sortColumn.sort === 'asc'}"/>
          <ma-svg-icon name="material-symbols:arrow-downward" size="20" class="sort_item ml-1"
                       :class="{'sort_checked': sortColumn.sort === 'desc'}"/>
        </div>
      </div>
      <el-table :data="orderList" row-key="id" ref="tableRef" :expand-row-keys="expandedRows">
        <el-table-column width="40" type="selection"/>
        <el-table-column width="1" type="expand" class-name="hidden-expand-icon">
          <template #default="scope">
            <div class="expand-content">
              <el-card v-for="(it,idx) in scope.row.orders" :key="idx" class="mb-2">
                <template #header>
                  <div class="flex_center">
                    <span>订单号：</span>
                    <span>{{ it.tid }}</span>
                    <span class="ml-5">付款时间：</span>
                    <span>{{ it.payTime }}</span>
                    <span class="ml-5">承诺揽收时间：</span>
                    <span>{{ it.lastShipTime }}</span>
                  </div>
                </template>
                <div class="flex">
                  <div>
                    <div v-for="(item,idx2) in it.goods" :key="idx2" class="flex_center">
                      <el-image style="width: 60px; height: 60px;min-width: 60px" :src="item.goodsImg"
                                :hide-on-click-modal="true" :preview-src-list="[it.goodsImg]"/>
                      <span class="ml-3">{{ item.goodsName }}</span>
                      <span class="ml-3"> ¥{{ item.price }}</span>
                      <span class="ml-1"> X {{ item.quantity }}</span>
                    </div>
                  </div>
                  <div class="right_price flex_center">
                    <div class="red">¥ {{ getTotalAmount(it) }}</div>
                  </div>
                </div>
              </el-card>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="订单来源" :show-overflow-tooltip="false" prop="source"/>
        <el-table-column label="订单号" :show-overflow-tooltip="false" prop="tid"/>
        <el-table-column label="收件信息" :show-overflow-tooltip="false" prop="receiverInfo"/>
        <el-table-column label="留言备注" :show-overflow-tooltip="false" prop="remark"/>
        <el-table-column label="物流公司" :show-overflow-tooltip="false" prop="company">
          <template #default="scope">
            <el-select placeholder="物流公司">
              <el-option value="SF" label="顺丰"/>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="运单号" :show-overflow-tooltip="false" prop="waybillCode"/>
        <el-table-column label="商品" :show-overflow-tooltip="false" min-width="200">
          <template #default="scope">
            <div class="flex_center" v-for="it in scope.row.goodsList" :key="it.skuId">
              <el-image
                style="width: 60px; height: 60px;min-width: 60px"
                :src="it.goodsImg"
                :hide-on-click-modal="true"
                :preview-src-list="[it.goodsImg]"
              />
              <div class="ml-2">
                <p>{{ it.goodsName }}</p>
                <p>X {{ it.quantity }}</p>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="下单时间/承诺揽收时间" :show-overflow-tooltip="false" prop="createTime"/>
        <el-table-column label="操作" :show-overflow-tooltip="false" width="120">
          <template #default="scope">
            <a @click="toggleExpand(scope.row)">
              {{ expandedRows.includes(scope.row.id) ? '收起' : '详情' }}
            </a>
            <a class="ml-2">拆单</a>
          </template>
        </el-table-column>
      </el-table>
    </div>

  </div>
  <div class="order-footer flex_center"
       :style="{'width': getMenuCollapseState() ? 'calc(100% - var(--mine-g-sub-aside-collapse-width) - 25px)' : 'calc(100% - var(--mine-g-sub-aside-width) - 25px)'}">
    <div>
      <span>已选</span>
      <span class="order_count">1</span>
      <span>笔</span>
    </div>
    <div class="ml-10">
      <el-button type="primary">打快递单</el-button>
      <el-button>打发货单</el-button>
      <el-button>发货</el-button>
      <el-button>选择/修改物流</el-button>
      <el-button>转为代发</el-button>
      <el-button>拆合单</el-button>
      <el-button>回收单号</el-button>
      <el-button>备注</el-button>
      <el-button>锁定订单</el-button>
    </div>
    <div class="ml-auto">
      <el-pagination
        v-model:current-page="pageReq.page"
        v-model:page-size="pageReq.pageSize"
        :page-sizes="[100, 200, 300, 400]"
        layout="total, sizes, prev, pager, next"
        :total="pageReq.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>


  </div>
  <el-dialog :title="modalObj.title" v-model="modalObj.visible" :width="modalObj.width" :close-on-click-modal="false">
    <template v-if="modalObj.type === 'setAllocation'">
      <div class="flex_center">
        <span>自动分配订单</span>
        <el-switch class="ml-3" active-text="已开启" inactive-text="已关闭" inline-prompt/>
      </div>
      <p class="red mt-1">开启自动分配后，厂家可直接同步到应分配给他的订单</p>
      <div class="flex_center mt-5">
        <el-checkbox>按商品所属关系自动分配给厂家</el-checkbox>
        <a class="ml-2">商品设置</a>
      </div>
      <div class="flex_center">
        <el-checkbox>按店铺推送给厂家</el-checkbox>
        <a class="ml-2" @click="setShopBindFactory">设置店铺绑定厂家</a>
        <a class="ml-2" @click="setExcludeGoodsRule">设置部分商品不推</a>
      </div>
      <div class="flex_center">
        <span>赠品跟随其他商品推送厂家</span>
        <el-switch class="ml-3" active-text="已开启" inactive-text="已关闭" inline-prompt/>
        <el-tooltip placement="top" effect="light">
          <template #content>
            <p>
              开启后<br/>
              只有一个商品，赠品优先跟随商品推送厂家，不按赠品绑定厂家推送；<br/>
              存在多个商品，赠品按绑定厂家推送。
            </p>
          </template>
          <ma-svg-icon name="material-symbols:help" class="ml-1 question-img"/>
        </el-tooltip>
      </div>
    </template>
    <template v-else-if="modalObj.type === 'shopBindFactory'">
      <ma-table :data="shopList" :columns="shopBindColumns">
        <template #column-index="scope">
          <p>{{ scope.$index + 1 }}</p>
        </template>
        <template #column-factory="scope">
          <el-select/>
        </template>
      </ma-table>
    </template>
    <template v-else-if="modalObj.type === 'excludeGoodsRule'">
      <el-button type="primary" size="small">添加商品</el-button>
      <ma-table :data="goodsList" :columns="excludeGoodsColumns" class="mt-2">
        <template #column-opt="scope">
          <a class="red" @click="removeGoods(scope.$index)">删除</a>
        </template>
      </ma-table>
    </template>
    <template v-else-if="modalObj.type === 'mergeOrderSet'">
      <div class="flex_center">
        <span>自动合单</span>
        <el-switch class="ml-3" active-text="已开启" inactive-text="已关闭" inline-prompt/>
      </div>
      <!--        <p class="red mt-1">合并订单不支持拆单</p>-->
      <el-checkbox disabled v-model="mergeOrderFlag">店铺、收件人、手机/电话、地址、平台一致</el-checkbox>
    </template>
    <template #footer>
      <el-button @click="modalObj.visible = false">取 消</el-button>
      <el-button type="primary" @click="asyncOk">确 定</el-button>
    </template>
  </el-dialog>
</template>

<style lang="scss">
.hidden-expand-icon .el-table__expand-icon {
  display: none;
}

.hidden-expand-icon {
  width: 0;
  padding: 0;
}

.order-footer {
  height: 60px;
  position: fixed;
  bottom: 0;
  background: white;
  z-index: 111;
  right: 0;
  padding: 0 1rem;
  column-gap: 0.25rem;

  .order_count {
    margin: 0 0.2rem;
    font-size: 20px;
    color: var(--el-color-primary);
  }
}

.print-send-order {
  margin: 0.75rem;

  .expand-content {
    padding: 0.5rem;

    .right_price {
      width: 120px;
      border-left: 1px solid #ccc;
      margin-left: auto;
      padding-left: 1rem;
    }
  }

  .sort_item {
    cursor: pointer;
    width: 24px;
    height: 24px;
    line-height: 24px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #ccc;
    transition: all .5s;
    color: #ccc;
  }

  .sort_checked {
    color: #fff;
    border: none;
    background: var(--el-color-primary);
  }

  .order-table {
    background: white;
    padding: 0 0.75rem 5rem 0.75rem;
    margin: 0.75rem 0;

    .order-opt-content {
      height: 60px;
      line-height: 30px;
      font-size: 14px;
    }
  }

  .tab-select {
    background: white;
    padding: 0.75rem;
  }

  .opt-button {
    position: absolute;
    right: 1rem;
    top: 0.75rem;
  }
}


a {
  color: var(--el-color-primary);
  cursor: pointer;
}

.red {
  color: red;
}

.ml-auto {
  margin-left: auto;
}

.flex_center {
  display: flex;
  align-items: center;
}

.question-img {
  font-size: 20px;
  cursor: pointer;
}
</style>
