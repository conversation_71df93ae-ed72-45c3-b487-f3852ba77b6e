<script setup>
import CustomOrder from '~/base/views/order/orderFree/index.vue'
import RefundOrder from '~/base/views/order/orderFree/refundIndex.vue'
import NotSend from "~/base/views/order/printSend/notSend.vue";
import HasSend from "~/base/views/order/printSend/hasSend.vue";
import Special from "~/base/views/order/printSend/special.vue";

const activeMode = ref(1)

function changeMode() {
  switch (activeMode.value) {
    case 1:
      notSendRef.value.init()
      break;
    case 2:
      hasSendRef.value.init()
      break;
    case 3:
      specialRef.value.init()
      break;
    case 4:
      break;
  }
}

const drawerObj = ref({
  visible: false,
  type: '',
  title: ''
})
const customOrderRef = ref()
const refundOrderRef = ref()

function addCustomOrder(item) {
  drawerObj.value.visible = true
  drawerObj.value.type = 'addCustom'
  drawerObj.value.title = '新建订单'
  nextTick(() => {
    let obj = null
    if (item) {
      obj = {tid: item.tid, shopId: item.shop_id}
    }
    customOrderRef.value.initFooter(false, obj)
  })
}

function addRefund(item) {
  drawerObj.value.visible = true
  drawerObj.value.type = 'addRefund'
  drawerObj.value.title = '新建换货/补寄订单'
  nextTick(() => {
    refundOrderRef.value.init(item)
  })
}
const notSendRef= ref()
const hasSendRef= ref()
const specialRef= ref()

async function createCustomOrder(continueCreate){
  const res = await customOrderRef.value.createOrder(true)
  if (res && !continueCreate) {
    drawerObj.value.visible = false
  }
  switch (activeMode.value) {
    case 1:
      notSendRef.value.init()
      break;
    case 2:
      hasSendRef.value.init()
      break;
  }
}

</script>

<template>
  <div>
    <div class="print-send-order">
      <el-tabs v-model="activeMode" @tab-change="changeMode" class="tab-select">
        <el-tab-pane label="待发货" :name="1"/>
        <el-tab-pane label="已发货" :name="2"/>
        <el-tab-pane label="特殊订单" :name="3"/>
        <el-tab-pane label="异常订单" :name="4"/>
      </el-tabs>
      <div class="opt-button">
        <el-button type="primary">同步订单</el-button>
        <el-button type="primary" class="ml-1" @click="addCustomOrder(null)" v-if="activeMode === 1">新建订单</el-button>
        <el-button type="primary" class="ml-1" @click="addRefund(null)" v-if="activeMode === 2">新建换货/补寄订单</el-button>
      </div>
    </div>
    <not-send v-if="activeMode === 1" ref="notSendRef"/>
    <has-send v-else-if="activeMode === 2" @addRefund="addRefund" @addCustom="addCustomOrder" ref="hasSendRef"/>
    <special v-else-if="activeMode === 3" ref="specialRef"/>
    <el-drawer v-model="drawerObj.visible" :title="drawerObj.title" :close-on-click-modal="false" size="80%"
               append-to-body="true">
      <template #default>
        <template v-if="drawerObj.type === 'addCustom'">
          <CustomOrder ref="customOrderRef"/>
        </template>
        <template v-else-if="drawerObj.type === 'addRefund'">
          <RefundOrder ref="refundOrderRef"/>
        </template>
      </template>
      <template #footer>
        <el-button type="primary" v-if="drawerObj.type === 'addCustom'" @click="createCustomOrder(true)">保存并继续创建</el-button>
        <el-button type="primary" plain @click="createCustomOrder(false)">保存</el-button>
        <el-button @click="drawerObj.visible = false">关闭</el-button>
      </template>
    </el-drawer>
  </div>
</template>

<style lang="scss">
.hidden-expand-icon .el-table__expand-icon {
  display: none;
}

.el-drawer__header {
  margin-bottom: 0 !important;
}

.hidden-expand-icon {
  width: 0;
  padding: 0;
}

.print-send-order {
  margin: 0.75rem;
  .tab-select {
    background: white;
    padding: 0.75rem;
  }

  .opt-button {
    position: absolute;
    right: 1rem;
    top: 0.75rem;
  }
}
</style>
