.mine-tab-container {
  @apply relative;
}

.mine-tab-arrow {
  @apply flex b-b-1 b-[#e8e9ec] b-b-solid bg-[#f9fafc] dark-b-dark-5 dark-bg-dark-8 b-r-1 b-r-solid;

  div {
    @apply w-full h-full flex items-center transition-all duration-300 cursor-pointer;
  }

  div:hover {
    @apply dark-bg-dark-4 bg-white;
  }
}

.mine-tabbar {
  @apply text-sm flex items-center relative overflow-x-hidden w-full
    bg-[#f9fafc]
    dark-bg-dark-8
    b-b-1 b-b-solid b-[#e8e9ec] dark-b-dark-5 transition-all duration-300
  ;

  height: var(--mine-g-tabbar-height);

  & .tab-item {
    @apply bg-gray-1 dark-bg-dark-6
      cursor-default relative transition-all duration-300;

    & .title { @apply px-1.5 w-16 truncate text-sm; }

    & .title,
    & .menu-icon,
    & .icon {
      @apply text-gray-5 dark-text-gray-3;
    }

    & .number-icon {
      @apply h-3.5 w-3.5 flex items-center justify-center text-[12px] !text-dark-9 !dark-text-gray-1
      ring-1 ring-stone-6 bg-gray-1 dark-bg-dark-6;
    }

    & .icon, & .number-icon {
      @apply rounded-full p-[1px] mx-1 cursor-pointer transition-all duration-300 relative
        hover-ring-1 hover-ring-stone-6 hover-bg-gray-1 hover-text-dark-2 dark-hover-bg-dark-6
      ;
    }

    &.cardPlan {
      @apply py-1 flex items-center px-2 rounded ml-1.5
        ring-1 ring-[#e8e9ec] dark-ring-dark-5 transition-all duration-200 relative
      ;
    }

    &.rectanglePlan {
      @apply flex items-center h-full px-2 transition-all duration-200 relative
        ring-1 ring-[#e8e9ec] dark-ring-dark-5
      ;
    }

    &.cardPlan.active,
    &.rectanglePlan.active {
      @apply bg-white z-2
        dark-bg-dark-3 shadow-inset shadow-sm
      ;

      .icon,
      .menu-icon,
      .title {
        @apply text-dark-9 dark-text-gray-1;
      }
    }

    &.cardPlan:hover,
    &.rectanglePlan:hover {
      @apply bg-[#fafafa]
        dark-bg-dark-4
      ;

      .icon,
      .menu-icon,
      .title {
        @apply text-dark-9 dark-text-gray-1;
      }
    }
  }
}

.mine-tab-more-operation {
  @apply absolute right-2 top-[5px] rounded z-10 cursor-pointer transition-all duration-300
    shadow-sm ring-1
    ring-gray-2
    bg-white
    hover-bg-gray-2
    dark-ring-gray-6
    dark-bg-gray-7
    dark-hover-bg-gray-6
  ;
}

.mine-tab-container {
  .tabbar-move,
  .tabbar-enter-active,
  .tabbar-leave-active {
    transition: all 0.2s;
  }

  .tabbar-enter-from,
  .tabbar-leave-to {
    opacity: 0;
    transform: translateY(-10px);
  }

  .tabbar-leave-active {
    transition: all -0.2s;
    transform: translateY(0px) translateX(0px);
    opacity: 0;
    position: absolute !important;
  }
}
