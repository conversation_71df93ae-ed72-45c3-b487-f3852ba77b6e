.app-container {
  @apply h-full relative overflow-hidden;

  & .mine-main {
    @apply w-full relative h-full overflow-y-auto overflow-x-hidden
      bg-gray-1 text-gray-8 dark-bg-dark-9 dark-text-gray-2
    ;

    transition: all 0.3s;
  }

  & .mine-bars {
    @apply sticky z-20 top-0 transition-all duration-300;
  }

  & .mine-worker-area {
    @apply relative z-10;

    transition: all 0.3s;
  }

  & .mine-aside {
    @apply relative z-30
    ;

    transition: width 0.3s, height 0.3s, top 0.3s, left 0.3s, opacity 0.3s, display 0.3s, transform 0.3s;
  }

  & .mine-wrapper-not-full {
    height: calc(100% - var(--mine-g-header-height));
  }

  & .mine-wrapper-not-full {
    height: calc(100% - var(--mine-g-header-height));
  }

  & .mine-wrapper-full {
    height: 100%;
  }

  & .mine-wrapper {
    @apply flex relative;

    transition: width 0.3s, transform 0.5s, box-shadow 0.3s, top 0.3s;

    & .mine-aside {
      @apply flex h-full;
    }
  }
}

.mine-back-top {
  @apply fixed bottom-6 right-10 z-1000
    h-10 w-10 flex cursor-pointer
    items-center justify-center rounded-full
  bg-white shadow-sm ring-1 ring-gray-2 hover-bg-gray-1
  dark-ring-dark-1 dark-hover-bg-dark-9/50 dark-bg-dark-6
  ;
}

.mine-desc-info {
  @apply flex justify-between items-center text-sm mt-2;

  & .mine-desc-label {
    @apply w-8/12 text-gray-8 dark-text-gray-2;
  }

  & .mine-desc-value {
    @apply w-4/12 text-gray-5 dark-text-gray-3 text-right;
  }
}

.mine-aside-animate-enter-active,
.mine-aside-animate-leave-active {
  transition: transform 0.3s;
}

.mine-aside-animate-enter-from,
.mine-aside-animate-leave-to {
  transform: translateX(calc((var(--mine-g-main-aside-width) + var(--mine-g-sub-aside-width)) * -1));
}

// 仅侧边栏黑暗模式
body.mine-aside-dark {
  .mine-main-aside-content {
    background: rgb(15 15 15 / var(--un-bg-opacity));
    border-right-color: rgb(28 28 30 / var(--un-border-right-opacity));
  }
  .mine-main-aside-list {
    a {
      color: rgb(229 231 235 / var(--un-text-opacity)); /* #e5e7eb */;
    }
    a:hover {
      background-color: rgb(var(--ui-primary) / 30%); /* rgb(var(--ui-primary)/30%) */
      color: rgb(229 231 235 / var(--un-text-opacity)); /* #e5e7eb */
    }
    a.active {
      background-color: rgb(var(--ui-primary) / 70%); /* rgb(var(--ui-primary)/70%) */
    }
  }

  .mine-header-main {
    background-color: rgb(15 15 15 / var(--un-bg-opacity));
    border-bottom-color: rgb(28 28 30 / var(--un-border-right-opacity));
  }

  .mine-sub-aside-container,
  .mine-sub-aside {
    border-right-color: rgb(31 31 31 / 1);
    background-color: rgb(24 24 24 / var(--un-bg-opacity)); /* #181818 */

    & .mine-sub-aside-close-button,
    & .mine-sub-aside-collapse-button,
    & .mine-sub-aside-fixed-button {
      color: rgb(243 244 246 / var(--un-text-opacity));
      background-color: #2D2D2D;
    }
    & .mine-sub-aside-close-button:hover,
    & .mine-sub-aside-collapse-button:hover,
    & .mine-sub-aside-fixed-button:hover {
      color: rgb(243 244 246 / var(--un-text-opacity)); /* #f3f4f6 */
      background-color: rgb(60 60 60 / var(--un-bg-opacity)); /* #3c3c3c */
    }
  }

  .mine-toolbar-btn {
    color: #fff;
    background-color: rgb(31 31 31 / var(--un-bg-opacity)); /* #1f1f1f */;
    border-color: rgb(45 45 45 / var(--un-border-opacity));
  }

  .mine-menu-link, .mine-menu-link:hover, .mine-menu-link.active {
    color: #E5E7EB;
  }
  .mine-menu-item.active {
    .parentActive {
      @apply text-white;
    }
  }
  .mine-menu-link:hover {
    background-color: rgb(var(--ui-primary)/30%);
    color: #fff !important;
  }

  .mine-menu-link.active {
    background-color: rgb(var(--ui-primary)/70%);
  }

  .mine-menu-link.parentActive {
    @apply text-[rgb(var(--ui-primary))];
  }

  .mine-sub-menu {
    background-color: rgb(24 24 24 / var(--un-bg-opacity));
  }

  .mine-logo-title {
    color: #fff;
  }
}
